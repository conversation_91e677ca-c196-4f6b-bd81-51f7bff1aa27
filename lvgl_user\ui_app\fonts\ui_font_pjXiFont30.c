/*******************************************************************************
 * Size: 30 px
 * Bpp: 2
 * Opts: --bpp 2 --size 30 --font D:\Work\LVGL\LYwatch\assets\獅尾肉丸-Light.ttf -o D:\Work\LVGL\LYwatch\assets\ui_font_pjXiFont30.c --format lvgl --symbols 蓝牙开关飞行模式上报手动自信号卫星质量个数定位0123456789 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_PJXIFONT30
#define UI_FONT_PJXIFONT30 1
#endif

#if UI_FONT_PJXIFONT30

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0030 "0" */
    0x0, 0x5, 0x40, 0x0, 0xf, 0xaf, 0x0, 0x3,
    0xc0, 0x3c, 0x0, 0xf0, 0x0, 0xf0, 0x1e, 0x0,
    0xb, 0x42, 0xd0, 0x0, 0x78, 0x3c, 0x0, 0x3,
    0xc3, 0xc0, 0x0, 0x3c, 0x7c, 0x0, 0x3, 0xd7,
    0x80, 0x0, 0x2d, 0x78, 0x0, 0x2, 0xe7, 0x80,
    0x0, 0x2e, 0x78, 0x0, 0x2, 0xe7, 0x80, 0x0,
    0x2d, 0x7c, 0x0, 0x2, 0xd3, 0xc0, 0x0, 0x3d,
    0x3c, 0x0, 0x3, 0xc3, 0xc0, 0x0, 0x3c, 0x1e,
    0x0, 0x7, 0x80, 0xf0, 0x0, 0xf0, 0x7, 0x80,
    0x1d, 0x0, 0x2e, 0xb, 0x80, 0x0, 0x6f, 0xd0,
    0x0,

    /* U+0031 "1" */
    0x0, 0x4, 0x0, 0x0, 0x6f, 0x80, 0x3, 0xfb,
    0xd0, 0x0, 0x0, 0xb4, 0x0, 0x0, 0x2d, 0x0,
    0x0, 0xb, 0x40, 0x0, 0x2, 0xd0, 0x0, 0x0,
    0xb4, 0x0, 0x0, 0x2d, 0x0, 0x0, 0xb, 0x40,
    0x0, 0x2, 0xd0, 0x0, 0x0, 0xb4, 0x0, 0x0,
    0x2d, 0x0, 0x0, 0xb, 0x40, 0x0, 0x2, 0xd0,
    0x0, 0x0, 0xb4, 0x0, 0x0, 0x2d, 0x0, 0x0,
    0xb, 0x40, 0x0, 0x2, 0xd0, 0x0, 0x0, 0xb4,
    0x0, 0x0, 0x2d, 0x0, 0x0, 0x1f, 0x90, 0x7,
    0xff, 0xff, 0xf0,

    /* U+0032 "2" */
    0x0, 0x15, 0x40, 0x0, 0x3e, 0xbf, 0x40, 0xf,
    0x0, 0x3e, 0x3, 0xd0, 0x0, 0xf0, 0x7c, 0x0,
    0xf, 0x43, 0x40, 0x0, 0xb8, 0x0, 0x0, 0xb,
    0x80, 0x0, 0x0, 0xb8, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x2c, 0x0, 0x0,
    0x7, 0x40, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0xb, 0x0, 0x0, 0x1, 0xc0, 0x0,
    0x0, 0x74, 0x0, 0x0, 0xd, 0x0, 0x0, 0x3,
    0x40, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x28, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xfe, 0x7f, 0xff, 0xff,
    0xe0,

    /* U+0033 "3" */
    0x0, 0x15, 0x40, 0x0, 0x2e, 0xaf, 0x80, 0xf,
    0x0, 0x2e, 0x2, 0xe0, 0x0, 0xf4, 0x3d, 0x0,
    0x7, 0x80, 0x0, 0x0, 0x78, 0x0, 0x0, 0x7,
    0x80, 0x0, 0x0, 0xb4, 0x0, 0x0, 0x1e, 0x0,
    0x0, 0x1b, 0x40, 0x0, 0xbf, 0xc0, 0x0, 0x0,
    0x1b, 0x80, 0x0, 0x0, 0x1f, 0x0, 0x0, 0x0,
    0x78, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x3d,
    0x0, 0x0, 0x2, 0xd0, 0x0, 0x0, 0x3d, 0x78,
    0x0, 0x3, 0xc7, 0xc0, 0x0, 0x7c, 0x3d, 0x0,
    0xf, 0x0, 0xf4, 0x1b, 0xc0, 0x1, 0xff, 0xd0,
    0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0xb, 0x0, 0x0, 0x0, 0x1f, 0x0,
    0x0, 0x0, 0x3f, 0x0, 0x0, 0x0, 0xdf, 0x0,
    0x0, 0x2, 0x8f, 0x0, 0x0, 0x3, 0xf, 0x0,
    0x0, 0xc, 0xf, 0x0, 0x0, 0x28, 0xf, 0x0,
    0x0, 0x70, 0xf, 0x0, 0x0, 0xc0, 0xf, 0x0,
    0x3, 0x40, 0xf, 0x0, 0x6, 0x0, 0xf, 0x0,
    0xc, 0x0, 0xf, 0x0, 0x3e, 0xaa, 0xaf, 0xa9,
    0x3f, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0xf, 0x0,

    /* U+0035 "5" */
    0x7, 0xff, 0xff, 0xc0, 0xbf, 0xff, 0xfc, 0xa,
    0x0, 0x0, 0x0, 0xa0, 0x0, 0x0, 0xd, 0x0,
    0x0, 0x0, 0xd0, 0x0, 0x0, 0xd, 0x0, 0x0,
    0x0, 0xc0, 0x0, 0x0, 0xe, 0xff, 0xe0, 0x0,
    0xe5, 0x5f, 0xd0, 0x0, 0x0, 0x1f, 0x40, 0x0,
    0x0, 0x7c, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x2d, 0x0, 0x0, 0x2, 0xd0, 0x0, 0x0, 0x2d,
    0x0, 0x0, 0x3, 0xd7, 0x40, 0x0, 0x3c, 0xbc,
    0x0, 0x7, 0x83, 0xc0, 0x1, 0xf0, 0x1e, 0x1,
    0xbc, 0x0, 0x2f, 0xf9, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf8, 0x0,
    0x2, 0xe4, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x78,
    0x0, 0x0, 0x1e, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x2,
    0xd0, 0x0, 0x0, 0x3c, 0x2f, 0xf4, 0x3, 0xde,
    0x57, 0xe0, 0x7e, 0x0, 0xf, 0x87, 0x80, 0x0,
    0x3c, 0xb8, 0x0, 0x3, 0xd7, 0x80, 0x0, 0x2e,
    0x78, 0x0, 0x2, 0xe7, 0xc0, 0x0, 0x2e, 0x3c,
    0x0, 0x2, 0xd2, 0xd0, 0x0, 0x3c, 0x1f, 0x0,
    0x3, 0xc0, 0xb4, 0x0, 0xf0, 0x2, 0xe0, 0x7d,
    0x0, 0x7, 0xfe, 0x0,

    /* U+0037 "7" */
    0x7f, 0xff, 0xff, 0xd7, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x1, 0xc0, 0x0, 0x0, 0x28, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0xd,
    0x0, 0x0, 0x1, 0xc0, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x7, 0x40, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0xe, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x7, 0x40, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x1e, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0xb, 0x80, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x1f, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x15, 0x40, 0x0, 0x1f, 0xaf, 0x80, 0xb,
    0x40, 0x2e, 0x0, 0xe0, 0x0, 0xf0, 0x2c, 0x0,
    0x7, 0x42, 0xc0, 0x0, 0x78, 0x2d, 0x0, 0x7,
    0x41, 0xe0, 0x0, 0xb0, 0xf, 0x40, 0xe, 0x0,
    0x7d, 0x3, 0x80, 0x2, 0xfd, 0xd0, 0x0, 0x7,
    0xfd, 0x0, 0x0, 0xa7, 0xf8, 0x0, 0x74, 0x7,
    0xf0, 0x1d, 0x0, 0xf, 0x83, 0xc0, 0x0, 0x3c,
    0x78, 0x0, 0x2, 0xd7, 0x80, 0x0, 0x1d, 0x78,
    0x0, 0x2, 0xd3, 0xc0, 0x0, 0x3c, 0x2e, 0x0,
    0x7, 0x80, 0xb9, 0x6, 0xe0, 0x1, 0xbf, 0xe0,
    0x0,

    /* U+0039 "9" */
    0x0, 0x15, 0x0, 0x0, 0x2f, 0xaf, 0x0, 0xf,
    0x40, 0x3c, 0x2, 0xd0, 0x0, 0xf0, 0x3c, 0x0,
    0xb, 0x4b, 0x80, 0x0, 0x7c, 0xb4, 0x0, 0x3,
    0xcf, 0x40, 0x0, 0x3d, 0xb4, 0x0, 0x3, 0xdb,
    0x80, 0x0, 0x3d, 0x7c, 0x0, 0x3, 0xd2, 0xe0,
    0x0, 0x7c, 0xf, 0xd0, 0x6f, 0xc0, 0x2f, 0xfe,
    0x7c, 0x0, 0x4, 0xb, 0x80, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x1e, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0xf4, 0x0, 0x0, 0x2d, 0x0, 0x0, 0xf,
    0x40, 0x0, 0xb, 0xc0, 0x0, 0xb, 0xd0, 0x0,
    0x0, 0x40, 0x0, 0x0,

    /* U+4E0A "上" */
    0x0, 0x0, 0x0, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb0, 0x0, 0xa, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x2f, 0xc0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x64,
    0x0, 0x0, 0x0, 0xb0, 0x0, 0x1, 0xfd, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+4E2A "个" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x82,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x38, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0x0, 0x1e, 0x0, 0x0,
    0x0, 0x2, 0xc0, 0x0, 0x7, 0xc0, 0x0, 0x0,
    0xb, 0x0, 0x38, 0x1, 0xf4, 0x0, 0x0, 0x2c,
    0x0, 0x38, 0x0, 0x7e, 0x0, 0x0, 0xa0, 0x0,
    0x38, 0x0, 0xf, 0xe0, 0x3, 0x40, 0x0, 0x38,
    0x0, 0x2, 0xfd, 0x1c, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x7d, 0x90, 0x0, 0x0, 0x38, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x0, 0x0, 0x0,

    /* U+4F4D "位" */
    0x0, 0x0, 0x80, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0x0, 0xa, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x0, 0x1, 0xe0, 0x0, 0x0, 0x0, 0x7,
    0x40, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0xb0, 0x2, 0x80, 0x0, 0x1e, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x0, 0x3, 0xc2, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x3c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xc0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0xfc, 0x0, 0x80, 0x0,
    0x3, 0xc0, 0x0, 0x2a, 0xc0, 0x8, 0x0, 0x0,
    0x74, 0x0, 0x7, 0x2c, 0x0, 0x70, 0x0, 0xb,
    0x0, 0x0, 0xc2, 0xc0, 0x3, 0x40, 0x0, 0xf0,
    0x0, 0x24, 0x2c, 0x0, 0x2c, 0x0, 0xe, 0x0,
    0x1, 0x2, 0xc0, 0x1, 0xd0, 0x1, 0xd0, 0x0,
    0x0, 0x2c, 0x0, 0xe, 0x0, 0x2c, 0x0, 0x0,
    0x2, 0xc0, 0x0, 0xf0, 0x3, 0x80, 0x0, 0x0,
    0x2c, 0x0, 0xb, 0x0, 0x34, 0x0, 0x0, 0x2,
    0xc0, 0x0, 0xb4, 0x7, 0x0, 0x0, 0x0, 0x2c,
    0x0, 0x7, 0x40, 0xa0, 0x0, 0x0, 0x2, 0xc0,
    0x0, 0x70, 0xc, 0x0, 0x0, 0x0, 0x2c, 0x0,
    0x1, 0x0, 0xc0, 0x0, 0x0, 0x2, 0xc0, 0x0,
    0x0, 0x14, 0x0, 0xa0, 0x0, 0x2c, 0x0, 0x0,
    0x2, 0x0, 0x3f, 0xc0, 0x2, 0xc6, 0xaa, 0xaa,
    0xaa, 0xaa, 0xa8, 0x0, 0x2c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+4FE1 "信" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc0, 0x0, 0x34, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0xb4, 0x0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0xf, 0x0, 0x14, 0x0, 0xb, 0x0, 0x0,
    0x2, 0x80, 0x2f, 0xc0, 0x3, 0xc7, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x1, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x40, 0x0, 0x7c, 0x0, 0x0, 0x0, 0x0, 0xfc,
    0x0, 0x3f, 0x0, 0x7f, 0xff, 0xff, 0xff, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0xf8, 0x3, 0xb, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0x2, 0x2, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x0,
    0x0, 0x74, 0x0, 0xb, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0x0, 0x2, 0xc0, 0xe, 0x0, 0x0, 0xb,
    0x40, 0x0, 0xb0, 0x3, 0x80, 0x0, 0x2, 0xc0,
    0x0, 0x2c, 0x0, 0xe0, 0x0, 0x0, 0xb0, 0x0,
    0xb, 0x0, 0x38, 0x0, 0x0, 0x2c, 0x0, 0x2,
    0xc0, 0xe, 0x0, 0x0, 0xb, 0x0, 0x0, 0xb0,
    0x3, 0x80, 0x0, 0x2, 0xc0, 0x0, 0x2c, 0x0,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xb, 0x0, 0x38,
    0x0, 0x0, 0x2c, 0x0, 0x2, 0xc0, 0xd, 0x0,
    0x0, 0xa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+5173 "关" */
    0x0, 0x9, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0,
    0x2, 0xd0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0xf4, 0x0, 0xb, 0x40, 0x0, 0x0, 0x0, 0x3d,
    0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x0,
    0x2c, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0, 0x30,
    0x6, 0x40, 0x0, 0x0, 0x0, 0x0, 0xc0, 0xf,
    0xd0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x38, 0x0,
    0x1, 0xfc, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x7d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe2, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xc0, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x80, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x7, 0xd0,
    0x0, 0x0, 0xb, 0x80, 0x0, 0x0, 0xfd, 0x0,
    0x0, 0xb8, 0x0, 0x0, 0x0, 0x2f, 0xe4, 0xb,
    0x80, 0x0, 0x0, 0x0, 0x7, 0xfd, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x64,

    /* U+52A8 "动" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x40, 0xe, 0x0, 0x0, 0xa, 0xaa, 0xbf, 0x80,
    0xe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x0, 0x3d,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x1, 0x40, 0xd, 0x0, 0x38, 0x0, 0x0,
    0x3, 0xf0, 0xd, 0x0, 0x34, 0xbf, 0xff, 0xff,
    0xf0, 0xd, 0x0, 0x34, 0x0, 0x14, 0x0, 0x0,
    0x1d, 0x0, 0x34, 0x0, 0x3c, 0x0, 0x0, 0x1c,
    0x0, 0x34, 0x0, 0x38, 0x0, 0x0, 0x2c, 0x0,
    0x34, 0x0, 0xb0, 0x0, 0x0, 0x2c, 0x0, 0x74,
    0x0, 0xe0, 0x30, 0x0, 0x38, 0x0, 0x70, 0x1,
    0xc0, 0x1c, 0x0, 0x34, 0x0, 0x70, 0x3, 0x80,
    0xe, 0x0, 0xb0, 0x0, 0x70, 0x7, 0x0, 0xb,
    0x0, 0xe0, 0x0, 0xb0, 0xd, 0x0, 0x7, 0x81,
    0xc0, 0x0, 0xb0, 0x28, 0x1a, 0xfb, 0xc2, 0x80,
    0x0, 0xb0, 0x3f, 0xfd, 0x3, 0xc7, 0x0, 0x0,
    0xe0, 0x3d, 0x0, 0x2, 0x8d, 0x0, 0x0, 0xe0,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x1, 0xd0, 0x0,
    0x0, 0x0, 0xa0, 0x1, 0x57, 0xc0, 0x0, 0x0,
    0x2, 0x80, 0x0, 0xbf, 0x80, 0x0, 0x0, 0xc,
    0x0, 0x0, 0x2e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+536B "卫" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x80, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x2, 0xc0, 0x0, 0xb, 0x0, 0x0, 0x0, 0x2,
    0xc0, 0x0, 0xb, 0x0, 0x0, 0x0, 0x2, 0xc0,
    0x0, 0xb, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x0,
    0xb, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0xb,
    0x0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0xb, 0x0,
    0x0, 0x0, 0x2, 0xc0, 0x0, 0xb, 0x0, 0x0,
    0x0, 0x2, 0xc0, 0x0, 0xf, 0x0, 0x0, 0x0,
    0x2, 0xc0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x2,
    0xc0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x2, 0xc0,
    0x0, 0xe, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x0,
    0xe, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x1d,
    0x0, 0x0, 0x0, 0x2, 0xc0, 0x14, 0x2c, 0x0,
    0x0, 0x0, 0x2, 0xc0, 0x7, 0xf8, 0x0, 0x0,
    0x0, 0x2, 0xc0, 0x1, 0xe0, 0x0, 0x0, 0x0,
    0x2, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x0,
    0x0, 0x64, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x1,
    0xfe, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+53F7 "号" */
    0x0, 0xf, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0xd, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0xd,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0xd, 0x0,
    0x0, 0x0, 0x38, 0x0, 0x0, 0xd, 0x0, 0x0,
    0x0, 0x38, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0,
    0x38, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0, 0x38,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xd, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0,
    0x8, 0x0, 0x0, 0x0, 0x10, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbd, 0x2a, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xff, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x40, 0x0, 0x0, 0x1f, 0x40, 0x0,
    0x1f, 0xaa, 0xaa, 0xaa, 0xbe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x74, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x15, 0x2, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0x0, 0x0,

    /* U+5B9A "定" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x5, 0x0,
    0x0, 0xbc, 0x3, 0xea, 0xaa, 0xaa, 0xaa, 0xaa,
    0xfd, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf0,
    0x1f, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x7d,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0x0, 0x64, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x38,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0,
    0x0, 0x0, 0xb, 0x0, 0x38, 0x0, 0x14, 0x0,
    0x0, 0xb, 0x0, 0x38, 0x0, 0xbe, 0x0, 0x0,
    0xe, 0x0, 0x3f, 0xff, 0xff, 0x0, 0x0, 0xd,
    0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0x1c, 0x0,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x0, 0x38,
    0x0, 0x0, 0x0, 0x0, 0x36, 0x0, 0x38, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0x80, 0x38, 0x0, 0x0,
    0x0, 0x0, 0xd0, 0xa0, 0x38, 0x0, 0x0, 0x0,
    0x2, 0x80, 0x3c, 0x38, 0x0, 0x0, 0x0, 0x7,
    0x0, 0xb, 0xb8, 0x0, 0x0, 0x0, 0xc, 0x0,
    0x1, 0xff, 0xea, 0xaa, 0xa9, 0x20, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xfc, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+5F00 "开" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xe0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0xf, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0x2,
    0xc0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0xe, 0x0, 0x0, 0x0, 0x0, 0x2c, 0x0,
    0x3, 0x80, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0,
    0xe0, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x38,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0xe, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0x0, 0x3, 0x80, 0x0,
    0x0, 0x0, 0xb, 0x0, 0x0, 0xe0, 0x7, 0xe1,
    0xaa, 0xab, 0xea, 0xaa, 0xbe, 0xab, 0xfc, 0x0,
    0x0, 0xf0, 0x0, 0x1e, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0x3, 0x40,
    0x0, 0x38, 0x0, 0x0, 0x0, 0x1, 0xc0, 0x0,
    0xe, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x3,
    0x80, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0xe0,
    0x0, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0xe, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x3, 0x80, 0x0, 0x0,
    0xb, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0xa,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x1d, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0x18, 0x0, 0x0,
    0x0, 0x3, 0x40, 0x0, 0x0,

    /* U+5F0F "式" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xd0, 0xb8, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xd0, 0x1f, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xd0, 0x7, 0x80, 0x0, 0x0, 0x0, 0x1, 0xd0,
    0x1, 0x50, 0x0, 0x0, 0x0, 0x1, 0xd0, 0x0,
    0xfd, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x64,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc, 0x74,
    0x0, 0x0, 0xa, 0xab, 0xea, 0xa8, 0x38, 0x0,
    0x0, 0x0, 0x2, 0xc0, 0x0, 0x3c, 0x0, 0x0,
    0x0, 0x2, 0xc0, 0x0, 0x2c, 0x0, 0x0, 0x0,
    0x2, 0xc0, 0x0, 0x1d, 0x0, 0x0, 0x0, 0x2,
    0xc0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x2, 0xc0,
    0x0, 0xb, 0x0, 0x0, 0x0, 0x2, 0xc0, 0x0,
    0x3, 0x80, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x2,
    0xd0, 0x0, 0x0, 0x2, 0xc0, 0x1a, 0x0, 0xf0,
    0x4, 0x0, 0x2, 0xdb, 0xd0, 0x0, 0x7c, 0xc,
    0x0, 0x1b, 0xf4, 0x0, 0x0, 0x2f, 0xc, 0x1b,
    0xfe, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x2f, 0x80,
    0x0, 0x0, 0x0, 0x2, 0xfc, 0x4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7d, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+624B "手" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xa4, 0x0, 0x0, 0x1, 0xaf,
    0xfa, 0x5f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x2f, 0xc0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0xa0,
    0x0, 0x0, 0x0, 0xb, 0x0, 0x0, 0x3f, 0xc3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0xb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+62A5 "报" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0x0, 0xb, 0x80, 0x0, 0xb0,
    0x3, 0xea, 0xaa, 0xaf, 0x80, 0x0, 0xb0, 0x3,
    0xc0, 0x0, 0x7, 0x0, 0x0, 0xb0, 0x3, 0xc0,
    0x0, 0xb, 0x0, 0x0, 0xb1, 0xe3, 0xc0, 0x0,
    0xb, 0x0, 0x55, 0xb7, 0xf7, 0xc0, 0x0, 0xe,
    0x0, 0x55, 0xf9, 0x53, 0xc0, 0x0, 0xd, 0x0,
    0x0, 0xb0, 0x3, 0xc0, 0x1b, 0xfc, 0x0, 0x0,
    0xb0, 0x3, 0xc0, 0x0, 0xf4, 0x0, 0x0, 0xb0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x3,
    0xc0, 0x0, 0x3, 0xd0, 0x0, 0xb0, 0x63, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0xbb, 0x43, 0xc5, 0x0,
    0x7, 0x40, 0x1, 0xf8, 0x3, 0xc2, 0x0, 0xb,
    0x0, 0x2f, 0xf0, 0x3, 0xc2, 0x0, 0xe, 0x0,
    0xb8, 0xb0, 0x3, 0xc1, 0x80, 0x1d, 0x0, 0x20,
    0xb0, 0x3, 0xc0, 0xc0, 0x2c, 0x0, 0x0, 0xb0,
    0x3, 0xc0, 0x70, 0x34, 0x0, 0x0, 0xb0, 0x3,
    0xc0, 0x34, 0xb0, 0x0, 0x0, 0xb0, 0x3, 0xc0,
    0x1e, 0xd0, 0x0, 0x0, 0xb0, 0x3, 0xc0, 0xb,
    0xc0, 0x0, 0x0, 0xb0, 0x3, 0xc0, 0xb, 0xd0,
    0x0, 0x0, 0xb0, 0x3, 0xc0, 0x2c, 0xb8, 0x0,
    0x0, 0xb0, 0x3, 0xc0, 0xb0, 0x2f, 0x40, 0x6f,
    0xf0, 0x3, 0xc3, 0x80, 0x7, 0xf4, 0x3, 0xd0,
    0x3, 0xed, 0x0, 0x0, 0x74, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0,

    /* U+6570 "数" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd0, 0x0, 0x3, 0x40, 0x0, 0x0, 0x80,
    0x38, 0x28, 0x1, 0xe0, 0x0, 0x0, 0xd, 0xe,
    0xe, 0x0, 0x74, 0x0, 0x0, 0x2, 0xc3, 0x87,
    0x0, 0x2c, 0x0, 0x0, 0x0, 0x34, 0xe3, 0x44,
    0xf, 0x0, 0x0, 0x0, 0x4, 0x38, 0x4b, 0xd3,
    0x80, 0x2, 0xd0, 0xff, 0xff, 0xff, 0xf9, 0xd0,
    0x2, 0xfc, 0x0, 0x1f, 0x80, 0x0, 0xbf, 0xff,
    0xff, 0x0, 0xf, 0xfd, 0x0, 0x38, 0x0, 0xb0,
    0x0, 0x7, 0x78, 0xf0, 0xd, 0x0, 0x3c, 0x0,
    0x7, 0x4e, 0xf, 0xb, 0x80, 0xf, 0x0, 0x7,
    0x43, 0x81, 0xe3, 0x30, 0x3, 0x80, 0x7, 0x0,
    0xd0, 0x25, 0x88, 0x1, 0xd0, 0x5, 0x0, 0x90,
    0x0, 0x81, 0x40, 0xb0, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x30, 0x3c, 0x0, 0x0, 0x2c, 0x0, 0xa0,
    0xc, 0xe, 0x0, 0x1f, 0xff, 0xff, 0xfd, 0x2,
    0x8b, 0x0, 0x0, 0xb, 0x0, 0x2c, 0x0, 0x33,
    0xc0, 0x0, 0x7, 0x40, 0xe, 0x0, 0xb, 0xc0,
    0x0, 0x3, 0xc0, 0x7, 0x0, 0x1, 0xe0, 0x0,
    0x1, 0xe4, 0x3, 0x80, 0x0, 0xfc, 0x0, 0x0,
    0x6, 0xfa, 0xc0, 0x0, 0xb7, 0xc0, 0x0, 0x0,
    0xb, 0xf0, 0x0, 0xb0, 0x78, 0x0, 0x0, 0x2,
    0xef, 0x0, 0xb0, 0xb, 0x80, 0x0, 0x7, 0x80,
    0xe0, 0xa0, 0x0, 0xbc, 0x0, 0x1b, 0x40, 0x1,
    0xd0, 0x0, 0xb, 0xe0, 0xa8, 0x0, 0x2, 0x40,
    0x0, 0x0, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+661F "星" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x2c, 0x0,
    0x0, 0x0, 0x2c, 0x0, 0x0, 0x2c, 0x0, 0x0,
    0x0, 0x2c, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0x2c, 0x0,
    0x0, 0x2c, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x16, 0x40,
    0x2c, 0x0, 0x10, 0x0, 0x0, 0x3, 0x80, 0x2c,
    0x0, 0x1, 0x0, 0x0, 0xb, 0x0, 0x2c, 0x0,
    0xf, 0xd0, 0x0, 0x1f, 0xaa, 0xbf, 0xaa, 0xaf,
    0xe0, 0x0, 0x38, 0x0, 0x2c, 0x0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0x1,
    0xc0, 0x0, 0x2c, 0x0, 0x5, 0x0, 0x6, 0x0,
    0x0, 0x2c, 0x0, 0x2f, 0x80, 0x4, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x24,
    0x0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0xfd, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,

    /* U+6A21 "模" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb0, 0x0, 0xe0, 0x0, 0xd, 0x0, 0x0, 0xb0,
    0x0, 0xe0, 0x40, 0xd, 0x14, 0x0, 0xb0, 0x0,
    0xb3, 0xe0, 0xd, 0x7e, 0x0, 0xb0, 0x3f, 0xff,
    0xfb, 0xff, 0xfe, 0x0, 0xb0, 0x0, 0x70, 0x0,
    0x1c, 0x0, 0x0, 0xb0, 0x90, 0x74, 0x0, 0x2c,
    0x0, 0x0, 0xb3, 0xf8, 0x34, 0x0, 0x28, 0x0,
    0x7f, 0xff, 0xf4, 0x0, 0x0, 0x3, 0xd0, 0x0,
    0xf0, 0x2, 0xff, 0xff, 0xff, 0xf0, 0x1, 0xf0,
    0x2, 0xc0, 0x0, 0x3, 0x80, 0x1, 0xf8, 0x2,
    0xc0, 0x0, 0x3, 0x80, 0x2, 0xf7, 0x2, 0xea,
    0xaa, 0xab, 0x80, 0x3, 0xf3, 0xc2, 0xc0, 0x0,
    0x3, 0x80, 0x7, 0xb1, 0xe2, 0xc0, 0x0, 0x3,
    0x80, 0xa, 0xb0, 0xe2, 0xc0, 0x0, 0x3, 0x80,
    0xd, 0xb0, 0x2, 0xff, 0xff, 0xff, 0x80, 0x1c,
    0xb0, 0x2, 0xc0, 0x3c, 0x3, 0x80, 0x24, 0xb0,
    0x1, 0x40, 0x38, 0x0, 0x10, 0x30, 0xb0, 0x0,
    0x0, 0x74, 0x0, 0xfd, 0x80, 0xb0, 0x7f, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0xb0, 0x0, 0x0, 0xe0,
    0x0, 0x0, 0x0, 0xb0, 0x0, 0x2, 0xc1, 0x0,
    0x0, 0x0, 0xb0, 0x0, 0x7, 0x40, 0xb8, 0x0,
    0x0, 0xb0, 0x0, 0x1d, 0x0, 0x1f, 0x80, 0x0,
    0xb0, 0x0, 0xb4, 0x0, 0x3, 0xf0, 0x0, 0xb0,
    0x7, 0x80, 0x0, 0x0, 0xb8, 0x0, 0xa0, 0x64,
    0x0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+7259 "牙" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xe0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x1, 0xd0,
    0x0, 0xb, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0,
    0x2c, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x0, 0xe0, 0x0, 0x2, 0xc0, 0x0,
    0x0, 0x7, 0x40, 0x0, 0xb, 0x0, 0x0, 0x0,
    0x2c, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0xb0, 0x0, 0x0, 0x3, 0x40, 0x0,
    0x2, 0xc0, 0xb, 0xc0, 0x2d, 0x0, 0x0, 0xb,
    0x40, 0x7f, 0x81, 0xfa, 0xaa, 0xab, 0xfe, 0xaa,
    0xa9, 0x1, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7a, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xcb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0x2c, 0x0, 0x0, 0x0, 0x0, 0x3, 0xc0,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x2, 0xc0,
    0x0, 0x0, 0x0, 0x3, 0xc0, 0xb, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0x0, 0x2c, 0x0, 0x0, 0x0,
    0x7, 0x80, 0x0, 0xb0, 0x0, 0x0, 0x0, 0x74,
    0x0, 0x2, 0xc0, 0x0, 0x0, 0xa, 0x0, 0x0,
    0xb, 0x0, 0x0, 0x1, 0xd0, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0x24, 0x0, 0x1, 0xbf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+81EA "自" */
    0x0, 0x0, 0x90, 0x0, 0x0, 0x0, 0x1, 0xd0,
    0x0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x0, 0x0,
    0x3, 0x40, 0x0, 0x0, 0x0, 0x7, 0x0, 0x0,
    0xbc, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xe0, 0x0,
    0x0, 0x0, 0xb0, 0xe0, 0x0, 0x0, 0x0, 0xb0,
    0xe0, 0x0, 0x0, 0x0, 0xb0, 0xe0, 0x0, 0x0,
    0x0, 0xb0, 0xe0, 0x0, 0x0, 0x0, 0xb0, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xe0, 0x0, 0x0, 0x0,
    0xb0, 0xe0, 0x0, 0x0, 0x0, 0xb0, 0xe0, 0x0,
    0x0, 0x0, 0xb0, 0xe0, 0x0, 0x0, 0x0, 0xb0,
    0xe0, 0x0, 0x0, 0x0, 0xb0, 0xe0, 0x0, 0x0,
    0x0, 0xb0, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0,
    0x0, 0x0, 0x0, 0xb0, 0xe0, 0x0, 0x0, 0x0,
    0xb0, 0xe0, 0x0, 0x0, 0x0, 0xb0, 0xe0, 0x0,
    0x0, 0x0, 0xb0, 0xe0, 0x0, 0x0, 0x0, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0x0, 0x0,
    0x0, 0xb0, 0xd0, 0x0, 0x0, 0x0, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe0, 0x0, 0xa, 0x0, 0x0, 0x0, 0x0,
    0xe0, 0x0, 0xe, 0x0, 0x24, 0x0, 0x0, 0xe0,
    0x0, 0xe, 0x0, 0xfe, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0xe0, 0x0, 0xa,
    0x0, 0x0, 0x0, 0x0, 0xe0, 0x0, 0xa, 0x0,
    0x0, 0x0, 0x0, 0x14, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x80, 0x2c, 0x0, 0xe0, 0x0, 0x40, 0x0,
    0xd0, 0x2c, 0x1, 0xd0, 0x3, 0xf0, 0x0, 0xd0,
    0x2c, 0x2, 0xff, 0xff, 0xf4, 0x0, 0xd0, 0x2c,
    0x3, 0x40, 0x0, 0x0, 0x0, 0xd0, 0x2c, 0x7,
    0x9, 0x0, 0x0, 0x0, 0xd0, 0x2c, 0xd, 0x3,
    0x80, 0x0, 0x0, 0xd0, 0x2c, 0x18, 0x1, 0xe0,
    0x0, 0x0, 0xd0, 0x2c, 0x30, 0x0, 0xf0, 0x0,
    0x0, 0xd0, 0x2c, 0x50, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0x2c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0x80, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xc0, 0x1c, 0x0,
    0xb0, 0xb, 0x0, 0x2, 0xc0, 0x1c, 0x0, 0xb0,
    0xb, 0x0, 0x2, 0xc0, 0x1c, 0x0, 0xb0, 0xb,
    0x0, 0x2, 0xc0, 0x1c, 0x0, 0xb0, 0xb, 0x0,
    0x2, 0xc0, 0x1c, 0x0, 0xb0, 0xb, 0x10, 0x2,
    0xc0, 0x1c, 0x0, 0xb0, 0xb, 0xbd, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe,

    /* U+884C "行" */
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0xb, 0x80, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0xf0, 0x2, 0xaa, 0xaa, 0xaa, 0x90, 0x0, 0x2c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x80, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x90, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xe0, 0x0,
    0x0, 0x0, 0x1f, 0x80, 0x0, 0x3c, 0x1a, 0xaa,
    0xaa, 0xab, 0xfc, 0x0, 0xf, 0x0, 0x0, 0x0,
    0x2d, 0x0, 0x0, 0x2, 0xe0, 0x0, 0x0, 0x2,
    0xc0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x2, 0xc0,
    0x0, 0x3, 0x4e, 0x0, 0x0, 0x0, 0x2c, 0x0,
    0x0, 0xc0, 0xe0, 0x0, 0x0, 0x2, 0xc0, 0x0,
    0x20, 0xe, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0,
    0x0, 0xe0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x0,
    0xe, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x0,
    0xe0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0xe0,
    0x0, 0x0, 0x2, 0xc0, 0x0, 0x0, 0xe, 0x0,
    0x0, 0x0, 0x2c, 0x0, 0x0, 0x0, 0xe0, 0x0,
    0x0, 0x2, 0xc0, 0x0, 0x0, 0xe, 0x0, 0x0,
    0x55, 0x7c, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x0,
    0x7f, 0x80, 0x0, 0x0, 0xd, 0x0, 0x0, 0x1,
    0xe0, 0x0, 0x0,

    /* U+8D28 "质" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6b, 0x80, 0x0, 0x0,
    0x0, 0x5, 0xbf, 0xff, 0x90, 0x0, 0x95, 0xaf,
    0xfe, 0xa5, 0x0, 0x0, 0x0, 0xe5, 0x0, 0x1,
    0x80, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x2, 0xc0,
    0x0, 0x0, 0x0, 0xe0, 0x0, 0x2, 0xc0, 0x0,
    0x60, 0x0, 0xe0, 0x0, 0x2, 0xc0, 0x1, 0xfc,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0xe0, 0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0xe0,
    0x0, 0x3, 0x80, 0x0, 0x0, 0x0, 0xe0, 0x0,
    0x3, 0x40, 0x0, 0x0, 0x0, 0xd0, 0x0, 0x3,
    0x40, 0x7, 0xc0, 0x0, 0xd0, 0xbf, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0xd0, 0xb0, 0x0, 0x0, 0x7,
    0x40, 0x0, 0xd0, 0xb0, 0x1, 0x80, 0x7, 0x40,
    0x1, 0xd0, 0xb0, 0x2, 0xc0, 0x7, 0x40, 0x1,
    0xc0, 0xb0, 0x2, 0xc0, 0x7, 0x40, 0x1, 0xc0,
    0xb0, 0x3, 0xc0, 0x7, 0x40, 0x2, 0xc0, 0xb0,
    0x3, 0xc0, 0x7, 0x40, 0x3, 0x80, 0xb0, 0x3,
    0x80, 0x7, 0x40, 0x3, 0x40, 0xb0, 0xb, 0x0,
    0x7, 0x40, 0x3, 0x0, 0xb0, 0xe, 0x29, 0x7,
    0x40, 0xb, 0x0, 0x50, 0x3c, 0x7, 0xe4, 0x0,
    0xd, 0x0, 0x0, 0xf0, 0x0, 0x7e, 0x0, 0x1c,
    0x0, 0xb, 0x80, 0x0, 0xb, 0xd0, 0x34, 0x0,
    0xb8, 0x0, 0x0, 0x1, 0xf4, 0x60, 0x2e, 0x40,
    0x0, 0x0, 0x0, 0x38, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+91CF "量" */
    0x0, 0x1a, 0xaa, 0xaa, 0xaa, 0xfc, 0x0, 0x0,
    0x1d, 0x0, 0x0, 0x0, 0xb4, 0x0, 0x0, 0x1c,
    0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x1c, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x1c, 0x0, 0x0, 0x0, 0xb0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x2c, 0x0, 0x38, 0x0,
    0x38, 0x0, 0x0, 0x2c, 0x0, 0x38, 0x0, 0x38,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x2c, 0x0, 0x38, 0x0, 0x38, 0x0, 0x0,
    0x2c, 0x0, 0x38, 0x0, 0x38, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x2c, 0x0,
    0x38, 0x0, 0x34, 0x0, 0x0, 0x10, 0x0, 0x38,
    0x0, 0xf, 0xd0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0x14,
    0x0, 0x0, 0x0, 0x38, 0x0, 0x0, 0xfd, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+98DE "飞" */
    0x0, 0x0, 0x0, 0x3d, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x0, 0xb, 0x0, 0x0, 0x0, 0x0, 0x38,
    0x0, 0x1f, 0x0, 0x0, 0x0, 0x0, 0x38, 0x0,
    0x3c, 0x0, 0x0, 0x0, 0x0, 0x38, 0x0, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0x3, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x38, 0x28, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x38, 0xb8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c,
    0x1f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x3,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x2c, 0x0, 0xbd,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x1f, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0x0, 0xb, 0x80, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x2, 0x40, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xc0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x1, 0xf0,
    0x0, 0x14, 0x0, 0x0, 0x0, 0x0, 0xbd, 0x0,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe4, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 257, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 81, .adv_w = 257, .box_w = 13, .box_h = 23, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 156, .adv_w = 257, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 237, .adv_w = 257, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 318, .adv_w = 257, .box_w = 16, .box_h = 22, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 406, .adv_w = 257, .box_w = 14, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 483, .adv_w = 257, .box_w = 14, .box_h = 24, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 567, .adv_w = 257, .box_w = 14, .box_h = 22, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 644, .adv_w = 257, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 725, .adv_w = 257, .box_w = 14, .box_h = 24, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 809, .adv_w = 480, .box_w = 28, .box_h = 26, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 991, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1187, .adv_w = 480, .box_w = 30, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1390, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1601, .adv_w = 480, .box_w = 28, .box_h = 27, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1790, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1993, .adv_w = 480, .box_w = 28, .box_h = 25, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2168, .adv_w = 480, .box_w = 28, .box_h = 26, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2350, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2553, .adv_w = 480, .box_w = 29, .box_h = 26, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2742, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2945, .adv_w = 480, .box_w = 30, .box_h = 29, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3163, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3359, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3570, .adv_w = 480, .box_w = 28, .box_h = 26, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3752, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3955, .adv_w = 480, .box_w = 27, .box_h = 27, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4138, .adv_w = 480, .box_w = 20, .box_h = 28, .ofs_x = 6, .ofs_y = -3},
    {.bitmap_index = 4278, .adv_w = 480, .box_w = 28, .box_h = 27, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4467, .adv_w = 480, .box_w = 30, .box_h = 27, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4670, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4873, .adv_w = 480, .box_w = 28, .box_h = 26, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5055, .adv_w = 480, .box_w = 28, .box_h = 27, .ofs_x = 2, .ofs_y = -3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x20, 0x143, 0x1d7, 0x369, 0x49e, 0x561, 0x5ed,
    0xd90, 0x10f6, 0x1105, 0x1441, 0x149b, 0x1766, 0x1815, 0x1c17,
    0x244f, 0x33e0, 0x36d3, 0x3a42, 0x3f1e, 0x43c5, 0x4ad4
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 48, .range_length = 10, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 19978, .range_length = 19157, .glyph_id_start = 11,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 23, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 2,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t ui_font_pjXiFont30 = {
#else
lv_font_t ui_font_pjXiFont30 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 29,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 0,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if UI_FONT_PJXIFONT30*/

