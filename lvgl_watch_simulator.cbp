<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="LVGL Watch Simulator" />
		<Option pch_mode="2" />
		<Option compiler="gcc" />
		<Build>
			<Target title="Debug">
				<Option output="bin/Debug/lvgl_watch_simulator" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Debug/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-g" />
					<Add option="-std=c99" />
					<Add option="-Wall" />
					<Add option="-Wextra" />
					<Add option="-DLV_CONF_INCLUDE_SIMPLE" />
					<Add directory="." />
					<Add directory="lvgl" />
					<Add directory="lv_drivers" />
				</Compiler>
				<Linker>
					<Add library="gdi32" />
					<Add library="user32" />
					<Add library="winmm" />
				</Linker>
			</Target>
			<Target title="Release">
				<Option output="bin/Release/lvgl_watch_simulator" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Release/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-O2" />
					<Add option="-std=c99" />
					<Add option="-Wall" />
					<Add option="-DLV_CONF_INCLUDE_SIMPLE" />
					<Add directory="." />
					<Add directory="lvgl" />
					<Add directory="lv_drivers" />
				</Compiler>
				<Linker>
					<Add option="-s" />
					<Add library="gdi32" />
					<Add library="user32" />
					<Add library="winmm" />
				</Linker>
			</Target>
		</Build>
		<Compiler>
			<Add option="-Wall" />
		</Compiler>
		<Unit filename="main.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="my_gui.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="my_gui.h" />
		<Extensions>
			<lib_finder disable_auto="1" />
		</Extensions>
	</Project>
</CodeBlocks_project_file>
