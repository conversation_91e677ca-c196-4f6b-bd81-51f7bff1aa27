#include "user_display.h"
#include "../diver/timerDelay/tmdelay.h"
#include "../diver/ec800m/ec800m.h"
#include "../system_set.h"
#include "../diver/battery/battery.h"
#include "../diver/userkey/userkey.h"
#include "../diver/pingmu/pingmu.h"
#include "../diver/csi_sensor/csi_sensor.h"

uint8_t ping_mu_open_st = 1;//屏幕开关状态: 1亮屏，0熄屏

void _home_screen_updata(void)
{
    static uint32_t timer = 0;

    if(get_tmdelay_mstime() - timer >= 1000){
        timer = get_tmdelay_mstime();

        //闪烁时间冒号：
        static uint8_t time_dot_flash = 0;
        if(time_dot_flash){
            time_dot_flash = 0;
            lv_obj_clear_flag(ui_Dot1Panle, LV_OBJ_FLAG_HIDDEN);
            lv_obj_clear_flag(ui_Dot2Panle, LV_OBJ_FLAG_HIDDEN);
        }
        else{
            time_dot_flash = 1;
            lv_obj_add_flag(ui_Dot1Panle, LV_OBJ_FLAG_HIDDEN);
            lv_obj_add_flag(ui_Dot2Panle, LV_OBJ_FLAG_HIDDEN);
        }

        //更新时间显示：
        beijing_time_t t;
        get_beijing_time(&t, sys_unix_time);
        static uint8_t last_minu = 0xff;
        if(last_minu != t.min){
            lv_label_set_text_fmt(ui_DateLable, "%04d-%02d-%02d", t.year, t.month, t.day);
            lv_label_set_text_fmt(ui_HourLable, "%02d", t.hour);
            lv_label_set_text_fmt(ui_MinuLable, "%02d", t.min); 
        }

        //更新剂量率显示：
        static float last_dose_rate = 9999999.66f;
        if(last_dose_rate != csi_sensor.dose_rate){
            last_dose_rate = csi_sensor.dose_rate;
            char dose_rate_string[20];
            sprintf(dose_rate_string,"%0.2f", csi_sensor.dose_rate);
            lv_label_set_text(ui_DoseRateLable, dose_rate_string);
        }
        //PRINT("cnt:%d, cps: %0.2f\r\n", csi_sensor.cnt_now, csi_sensor.cps);

        //更新心率显示：

        //更新血氧显示：

        //更新电量显示：   
        static uint8_t last_bt_prt = 0;
        if(dev_battery_prt != last_bt_prt){
            last_bt_prt = dev_battery_prt;
            lv_label_set_text_fmt(ui_BatLable, "%d%%", last_bt_prt);
            lv_color_t bt_color = last_bt_prt > 10 ? lv_color_hex(0x24B600) : lv_color_hex(0xFF0000);
            lv_obj_set_style_text_color(ui_BatLable, bt_color, LV_PART_MAIN | LV_STATE_DEFAULT);
        }
        //充电期间：闪烁电量符号：
        if(charger_state){
             static uint8_t time_bt_flash = 0;
            if(time_bt_flash){
                time_bt_flash = 0;
                lv_obj_clear_flag(ui_Label2, LV_OBJ_FLAG_HIDDEN);
            }
            else{
                time_bt_flash = 1;
                lv_obj_add_flag(ui_Label2, LV_OBJ_FLAG_HIDDEN);
            }
        }
        else{
            lv_obj_clear_flag(ui_Label2, LV_OBJ_FLAG_HIDDEN);
        }
       
    }
}

void lv_switch_event_cb(lv_event_t * e)
{
    lv_obj_t * switch_tg = lv_event_get_target(e);

    uint8_t new_state = lv_obj_has_state(switch_tg, LV_STATE_CHECKED);

    if(switch_tg == ui_BTSwitch){
        system_setings.bt_switch = new_state;
    }
    else if(switch_tg == ui_FlyModeSwitch){
        system_setings.fly_mode_switch = new_state;
    }
    else if(switch_tg == ui_GpsSwitch){
        system_setings.gnss_switch = new_state;
    }
    else if(switch_tg == ui_SendDataNowSwitch){
        system_state.manual_upsend_switch = 1;
    }
    else if(switch_tg == ui_AutoSendSwitch){
        system_setings.auto_upsend_switch = new_state;
    }

    if(system_setings.fly_mode_switch){
        system_setings.bt_switch = 0;
        lv_obj_clear_state(ui_BTSwitch, LV_STATE_CHECKED);

        system_setings.gnss_switch = 0;
        lv_obj_clear_state(ui_GpsSwitch, LV_STATE_CHECKED);

        system_state.manual_upsend_switch = 0;
        lv_obj_clear_state(ui_SendDataNowSwitch, LV_STATE_CHECKED);                   
    }

    //保存到flash：
    if(switch_tg != ui_SendDataNowSwitch){//手动上报按键触发时不需要保存
        system_settings_saved();
    }  
}

void _sys_seting_screen_update(void)
{
    /**
     * 云上报图标显示：
     * 灰色慢闪：DTU开机初始化中...
     * 白色慢闪：DTU就绪空闲
     * 橙黄色快闪：DTU数据发送中...
     * 绿色常亮: DTU数据发送成功
     * 红色常亮: DTU数据发送失败
     * 蓝色慢闪: DTU关机中...
     * 不显示: DTU已关机
     */

    uint16_t flash_speed;//0表示不显示，1表示固定显示，其他则为闪烁间隔
    lv_color_t color;

    switch (default_ec800m.upsend_state)
    {
        case 0:
        {
            flash_speed = 1000;//慢闪
            color = lv_color_hex(0xFFFFFF);//白色
        }
        break;

        case 1:
        {
            flash_speed = 100;//快闪
            color = lv_color_hex(0xFFB600);//橙黄色
        }
        break;

        case 2:
        {
            flash_speed = 1;//常亮
            color = lv_color_hex(0x24B600);//绿色    
        }
        break;

        case 3:
        {
            flash_speed = 1;//常亮
            color = lv_color_hex(0xFF0000);//红色 
        }
        break;
    
        default:
        break;
    }

    if(default_ec800m_mc.now_state == POWER_OFF){
        flash_speed = 0;//常灭
    }else if(default_ec800m_mc.now_state == POWER_OFF_ing){
        color = lv_color_hex(0x0000FF);//蓝色
    }else if(default_ec800m_mc.now_state < IDEL){
        color = lv_color_hex(0x535351);//灰色
    }

    lv_obj_set_style_text_color(ui_UpSendLable, color, LV_PART_MAIN | LV_STATE_DEFAULT);

    if(flash_speed == 0){
        lv_obj_add_flag(ui_UpSendLable, LV_OBJ_FLAG_HIDDEN);
    }
    else if(flash_speed == 1){      
        lv_obj_clear_flag(ui_UpSendLable, LV_OBJ_FLAG_HIDDEN);   
    }
    else{    
        static uint32_t timer =  0;
        if(get_tmdelay_mstime() - timer >= flash_speed){
            timer = get_tmdelay_mstime();
            static uint8_t flash = 0;
            if(flash){
                flash = 0;
                lv_obj_clear_flag(ui_UpSendLable, LV_OBJ_FLAG_HIDDEN);
            }
            else{
                flash = 1;
                lv_obj_add_flag(ui_UpSendLable, LV_OBJ_FLAG_HIDDEN);
            }
        }      
    }

    if(system_state.manual_upsend_switch == 0){
        lv_obj_clear_state(ui_SendDataNowSwitch, LV_STATE_CHECKED);
    }
    
    //更新信号质量显示：
    static uint8_t last_csq = 0;
    if(last_csq != default_ec800m.csq){
        last_csq = default_ec800m.csq;
        lv_label_set_text_fmt(ui_CSQLabel, "%d", default_ec800m.csq);
    }

    //更新卫星个数显示：
    static uint8_t last_gnss_star_num = 0;
    if(last_gnss_star_num != default_ec800m.gnss_star_num){
        last_gnss_star_num = default_ec800m.gnss_star_num;
        lv_label_set_text_fmt(ui_GPSNumLabel, "%d", default_ec800m.gnss_star_num);
    }
    
}

uint8_t watch_power_status = 0;//

void user_display_updata(void)
{
    static uint32_t mp_t = 0;

    if(system_state.watch_power_state == 2){
        if(get_userkey()==0){//等待开机健释放后再切换画面
            return;
        }
        ui_HomeScreen_screen_init();
        _ui_screen_change(&ui_HomeScreen, LV_SCR_LOAD_ANIM_NONE, 0, 1000, &ui_HomeScreen_screen_init); 
        system_state.watch_power_state = 3;
    }

    if(system_state.watch_power_state == 3){
        if(ping_mu_open_st == 1){//屏幕亮屏状态
            lv_obj_t * now_screen =  lv_scr_act();            

            if(now_screen == ui_HomeScreen){
                static uint8_t first_start = 1;
                if(first_start){
                    first_start = 0;
                    lv_obj_del_async(ui_StartScreen);
                }
                _home_screen_updata();
            }
            else if(now_screen == ui_SysSetingScreen){
                _sys_seting_screen_update();   
            }  

            if(get_tmdelay_mstime() - mp_t >= 20000){//自动熄屏时长ms
                ping_mu_open_st = 0;
                pm_sleep_in();
            }
        }

        //有操作，重新累计熄屏时长：
        if(get_userkey() == 0) mp_t = get_tmdelay_mstime();
        if(pm_touch_state == 1) mp_t = get_tmdelay_mstime();

        //按键：
        uint8_t key_state = sacn_userkey();
        if(key_state == 1){
            if(ping_mu_open_st){
                ping_mu_open_st = 0;
                pm_sleep_in();
            }
            else{
                ping_mu_open_st = 1;
                pm_sleep_out();
            }
        }
        else if(key_state == 2){//关机动作
            system_settings_saved();//保存数据
            if(default_ec800m_mc.now_state != POWER_OFF){
                default_ec800m_mc.now_state = POWER_OFF_ing;
            }
            if(ping_mu_open_st == 0){
                ping_mu_open_st = 1;
                pm_sleep_out();
            }
            system_state.watch_power_state = 4;

            lv_obj_t * now_screen =  lv_scr_act();
            lv_obj_del_async(now_screen);
            ui_StartScreen_screen_init();
            lv_disp_load_scr(ui_StartScreen);
        }
    }
    else if(system_state.watch_power_state == 5){
        if(default_ec800m_mc.now_state == POWER_OFF){
            while(get_userkey() == 0){
                system_wdog_feed(0);
            };//等待释放
            system_state.watch_power_state = 0;
            pm_fill_color(0);
            pm_sleep_in();

            //跳转到内置bootloader引导程序，若无代码升级，后续会再次进入本APP的main开头，实现关机效果
            //若有代码升级，在点击升级软件的“下载”按钮后，进行关机操作，可实现连接下载，不需重新上电，便于手表整机装配后的升级
            EnterCodeUpgrade();
            
        }
    }
}





