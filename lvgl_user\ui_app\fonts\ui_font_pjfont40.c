/*******************************************************************************
 * Size: 40 px
 * Bpp: 2
 * Opts: --bpp 2 --size 40 --font D:\Work\LVGL\LYwatch\assets\思源黑体TW-Heavy.otf -o D:\Work\LVGL\LYwatch\assets\ui_font_pjfont40.c --format lvgl --symbols 系统 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_PJFONT40
#define UI_FONT_PJFONT40 1
#endif

#if UI_FONT_PJFONT40

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+7CFB "系" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xbf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x1, 0x6b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xa5, 0x7, 0xff, 0xfe, 0x1,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xc0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xf8, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xea, 0xbf, 0xff, 0xd0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x24, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0x8b, 0xf0, 0x0, 0x0,
    0x0, 0x3f, 0xa6, 0xff, 0xff, 0xe1, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfd, 0x3,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x7, 0xff, 0xf0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xaf, 0xff, 0x40, 0x3, 0xff,
    0xff, 0xeb, 0xff, 0xc0, 0x0, 0x2f, 0xe0, 0x0,
    0xb, 0xbd, 0x0, 0xf, 0xff, 0x0, 0x7e, 0x3c,
    0x0, 0x0, 0x1, 0xff, 0x40, 0x3f, 0xfc, 0xf,
    0xff, 0x40, 0x0, 0x0, 0x1f, 0xff, 0xd0, 0xff,
    0xf1, 0xff, 0xff, 0x40, 0x0, 0x2, 0xff, 0xfe,
    0x3, 0xff, 0xc2, 0xff, 0xff, 0x80, 0x0, 0x2f,
    0xff, 0xe0, 0xf, 0xff, 0x1, 0xff, 0xff, 0xc0,
    0x7, 0xff, 0xfe, 0x0, 0x3f, 0xfc, 0x0, 0xff,
    0xff, 0xc0, 0xbf, 0xff, 0xea, 0xaa, 0xff, 0xf0,
    0x0, 0xbf, 0xff, 0x80, 0xff, 0xfe, 0x1f, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xf8, 0x0, 0xbf, 0xd0,
    0x3f, 0xff, 0xfe, 0x0, 0x0, 0xbf, 0x40, 0x0,
    0x7d, 0x0, 0xbf, 0xff, 0xf4, 0x0, 0x0, 0xa0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EDF "统" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xe0, 0x0, 0x0, 0x0,
    0x6f, 0xc0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x40,
    0x0, 0x2, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xc0, 0x0, 0x1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x80, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x0,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x7f,
    0xfc, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xbf, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xf2, 0xc0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x2, 0xff, 0xd3, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x7, 0xff,
    0xcb, 0xff, 0x40, 0x3f, 0xff, 0x0, 0x70, 0x0,
    0x1f, 0xff, 0x4f, 0xff, 0x40, 0xbf, 0xfd, 0x7,
    0xf8, 0x0, 0x3f, 0xff, 0xff, 0xff, 0x1, 0xff,
    0xf8, 0x2f, 0xfd, 0x0, 0x3f, 0xff, 0xff, 0xfd,
    0x3, 0xff, 0xf0, 0xf, 0xff, 0x0, 0x2f, 0xff,
    0xff, 0xf8, 0xf, 0xff, 0xc0, 0xb, 0xff, 0x80,
    0xf, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xef, 0xff,
    0xff, 0xc0, 0xf, 0xff, 0xff, 0xe3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0xb, 0xaf, 0xff, 0xc3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x1f,
    0xff, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x3f, 0xfd, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xfc, 0x0, 0xff, 0xfe, 0xfd, 0xff, 0xff,
    0xa5, 0x0, 0x1f, 0xf0, 0x2, 0xff, 0xff, 0xfc,
    0xa6, 0xff, 0xc3, 0xff, 0x8f, 0x40, 0x1f, 0xff,
    0xff, 0xfc, 0x2, 0xff, 0xc3, 0xff, 0xc0, 0x0,
    0x2f, 0xff, 0xff, 0xfc, 0x2, 0xff, 0xc3, 0xff,
    0xc0, 0x0, 0x1f, 0xff, 0xff, 0xfc, 0x2, 0xff,
    0xc3, 0xff, 0xc0, 0x0, 0xf, 0xff, 0xfe, 0x40,
    0x3, 0xff, 0xc3, 0xff, 0xc0, 0x0, 0xb, 0xfe,
    0x40, 0x0, 0x3, 0xff, 0xc3, 0xff, 0xc0, 0x0,
    0x7, 0x80, 0x0, 0x6d, 0x7, 0xff, 0x83, 0xff,
    0xc5, 0x0, 0x0, 0x0, 0xb, 0xfe, 0x7, 0xff,
    0x43, 0xff, 0xc7, 0xd0, 0x0, 0x1, 0xff, 0xff,
    0xf, 0xff, 0x43, 0xff, 0xc7, 0xfd, 0x0, 0x6f,
    0xff, 0xff, 0x1f, 0xff, 0x3, 0xff, 0xcb, 0xfd,
    0xb, 0xff, 0xff, 0xff, 0x7f, 0xfe, 0x3, 0xff,
    0xcb, 0xfd, 0x1f, 0xff, 0xff, 0xf5, 0xff, 0xfc,
    0x3, 0xff, 0xcf, 0xfc, 0xf, 0xff, 0xfe, 0xb,
    0xff, 0xf8, 0x3, 0xff, 0xff, 0xfc, 0xf, 0xff,
    0xe0, 0x2f, 0xff, 0xf0, 0x2, 0xff, 0xff, 0xf8,
    0xb, 0xfd, 0x0, 0xb, 0xff, 0xd0, 0x1, 0xff,
    0xff, 0xf4, 0x7, 0x90, 0x0, 0x3, 0xff, 0x40,
    0x0, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x0, 0x2f, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 640, .box_w = 39, .box_h = 40, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 390, .adv_w = 640, .box_w = 40, .box_h = 40, .ofs_x = 0, .ofs_y = -5}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x1e4
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 31995, .range_length = 485, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 2, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 2,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t ui_font_pjfont40 = {
#else
lv_font_t ui_font_pjfont40 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 40,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -5,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if UI_FONT_PJFONT40*/

