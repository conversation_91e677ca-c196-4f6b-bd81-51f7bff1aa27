#!/bin/bash

echo "===================="
echo "LVGL手表模拟器构建脚本"
echo "===================="

# 检查SDL2是否安装
check_sdl2() {
    if ! pkg-config --exists sdl2; then
        echo "❌ SDL2未安装，请先安装SDL2开发库："
        echo ""
        echo "Ubuntu/Debian:"
        echo "  sudo apt-get update"
        echo "  sudo apt-get install libsdl2-dev"
        echo ""
        echo "CentOS/RHEL:"
        echo "  sudo yum install SDL2-devel"
        echo ""
        echo "macOS:"
        echo "  brew install sdl2"
        echo ""
        echo "Windows (MinGW):"
        echo "  pacman -S mingw-w64-x86_64-SDL2"
        echo ""
        return 1
    else
        echo "✅ SDL2已安装"
        return 0
    fi
}

# 创建简化的Makefile
create_makefile() {
    cat > Makefile.simple << 'EOF'
# 简化的LVGL模拟器Makefile

CC = gcc
CFLAGS = -std=c99 -Wall -Wextra -O2 -g
CFLAGS += -I./lvgl -I./ -I./lvgl_user
CFLAGS += $(shell pkg-config --cflags sdl2)

LDFLAGS = $(shell pkg-config --libs sdl2) -lm

# 定义颜色
define ECHO_COLOR
	@echo -e "\033[1;32m$(1)\033[0m"
endef

# LVGL核心源文件
LVGL_CORE := $(wildcard lvgl/src/core/*.c)
LVGL_DRAW := $(wildcard lvgl/src/draw/*.c)
LVGL_DRAW_SW := $(wildcard lvgl/src/draw/sw/*.c)
LVGL_HAL := $(wildcard lvgl/src/hal/*.c)
LVGL_MISC := $(wildcard lvgl/src/misc/*.c)
LVGL_WIDGETS := $(wildcard lvgl/src/widgets/*.c)
LVGL_FONT := $(wildcard lvgl/src/font/*.c)

# LVGL额外组件
LVGL_EXTRA_WIDGETS := $(wildcard lvgl/src/extra/widgets/*/*.c)
LVGL_EXTRA_THEMES := $(wildcard lvgl/src/extra/themes/*/*.c)
LVGL_EXTRA_LAYOUTS := $(wildcard lvgl/src/extra/layouts/*/*.c)

# UI应用源文件
UI_SOURCES := $(wildcard lvgl_user/ui_app/*.c)
UI_SOURCES += $(wildcard lvgl_user/ui_app/screens/*.c)
UI_SOURCES += $(wildcard lvgl_user/ui_app/components/*.c)
UI_SOURCES += $(wildcard lvgl_user/ui_app/fonts/*.c)

# 主模拟器文件
MAIN_SRC = simulator_main.c

# 所有源文件
SOURCES = $(LVGL_CORE) $(LVGL_DRAW) $(LVGL_DRAW_SW) $(LVGL_HAL) $(LVGL_MISC) 
SOURCES += $(LVGL_WIDGETS) $(LVGL_FONT) $(LVGL_EXTRA_WIDGETS) $(LVGL_EXTRA_THEMES) $(LVGL_EXTRA_LAYOUTS)
SOURCES += $(UI_SOURCES) $(MAIN_SRC)

# 目标文件
OBJECTS = $(SOURCES:.c=.o)

# 可执行文件名
TARGET = lvgl_simulator

.PHONY: all clean run install-deps

all: $(TARGET)
	$(call ECHO_COLOR,"✅ 构建完成! 运行: ./$(TARGET)")

$(TARGET): $(OBJECTS)
	$(call ECHO_COLOR,"🔗 链接可执行文件...")
	$(CC) $(OBJECTS) -o $@ $(LDFLAGS)

%.o: %.c
	$(call ECHO_COLOR,"📝 编译: $<")
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	$(call ECHO_COLOR,"🧹 清理构建文件...")
	rm -f $(OBJECTS) $(TARGET)

run: $(TARGET)
	$(call ECHO_COLOR,"🚀 启动模拟器...")
	./$(TARGET)

install-deps:
	$(call ECHO_COLOR,"📦 检查SDL2安装...")
	@if ! pkg-config --exists sdl2; then \
		echo "请手动安装SDL2开发库"; \
		echo "Ubuntu/Debian: sudo apt-get install libsdl2-dev"; \
		echo "macOS: brew install sdl2"; \
		exit 1; \
	else \
		echo "✅ SDL2已安装"; \
	fi

EOF
    echo "✅ 创建了简化的Makefile"
}

# 使用模拟器配置替换原配置
use_simulator_config() {
    if [ -f "lvgl/lv_conf.h" ]; then
        cp lvgl/lv_conf.h lvgl/lv_conf.h.backup
        echo "✅ 备份了原配置文件"
    fi
    
    if [ -f "lv_conf_simulator.h" ]; then
        cp lv_conf_simulator.h lvgl/lv_conf.h
        echo "✅ 使用模拟器配置文件"
    fi
}

# 检查必需文件
check_files() {
    local missing_files=()
    
    if [ ! -f "simulator_main.c" ]; then
        missing_files+=("simulator_main.c")
    fi
    
    if [ ! -d "lvgl" ]; then
        missing_files+=("lvgl目录")
    fi
    
    if [ ! -d "lvgl_user" ]; then
        missing_files+=("lvgl_user目录")
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo "❌ 缺少必需文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        return 1
    fi
    
    return 0
}

# 主流程
main() {
    echo "🔍 检查项目文件..."
    if ! check_files; then
        exit 1
    fi
    
    echo "🔍 检查SDL2..."
    if ! check_sdl2; then
        exit 1
    fi
    
    echo "📝 创建构建文件..."
    create_makefile
    
    echo "⚙️  配置模拟器..."
    use_simulator_config
    
    echo ""
    echo "🏗️  开始构建..."
    make -f Makefile.simple clean
    make -f Makefile.simple
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "🎉 构建成功!"
        echo "🚀 运行模拟器: ./lvgl_simulator"
        echo "💡 提示: 鼠标左键模拟触摸操作"
    else
        echo "❌ 构建失败，请检查错误信息"
        exit 1
    fi
}

# 运行主程序
main "$@" 