@echo off
echo ========================================
echo       LVGL Simulator Build and Run
echo ========================================
echo.

REM Set paths
set SDL2_DIR=C:\SDL2
set SDL2_BIN=%SDL2_DIR%\x86_64-w64-mingw32\bin
set SDL2_LIB=%SDL2_DIR%\x86_64-w64-mingw32\lib
set SDL2_INC=%SDL2_DIR%\x86_64-w64-mingw32\include
set MINGW_BIN=C:\Users\<USER>\Desktop\mingW\mingw64\bin

echo Checking environment...

REM Check MinGW
if not exist "%MINGW_BIN%\gcc.exe" (
    echo ERROR: MinGW not found at %MINGW_BIN%
    pause
    exit /b 1
)
echo OK: <PERSON><PERSON><PERSON> found

REM Check SDL2
if not exist "%SDL2_BIN%\SDL2.dll" (
    echo ERROR: SDL2 not found at %SDL2_BIN%
    pause
    exit /b 1
)
echo OK: SDL2 found

REM Set PATH
set PATH=%MINGW_BIN%;%SDL2_BIN%;%PATH%

REM Check project files
if not exist "simulator_main.c" (
    echo ERROR: simulator_main.c not found
    pause
    exit /b 1
)
if not exist "lvgl" (
    echo ERROR: lvgl directory not found
    pause
    exit /b 1
)
echo OK: Project files found

REM Copy SDL2.dll
copy "%SDL2_BIN%\SDL2.dll" "." >nul 2>&1

REM Configure LVGL
if exist "lv_conf_simulator.h" (
    if exist "lvgl\lv_conf.h" (
        copy "lvgl\lv_conf.h" "lvgl\lv_conf.h.backup" >nul 2>&1
    )
    copy "lv_conf_simulator.h" "lvgl\lv_conf.h" >nul 2>&1
    echo OK: LVGL configured for simulator
)

REM Clean old files
del /Q *.o lvgl_simulator.exe 2>nul

echo.
echo Building...
echo.

REM Compile flags
set CFLAGS=-std=c99 -O2 -g -Wall -Wno-unused-parameter
set INCLUDES=-I./lvgl -I./ -I./lvgl_user -I%SDL2_INC%
set LIBS=-L%SDL2_LIB% -lmingw32 -lSDL2main -lSDL2 -lm

REM Compile LVGL core
echo Compiling LVGL core...
gcc %CFLAGS% %INCLUDES% -c lvgl/src/core/*.c
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile LVGL core
    pause
    exit /b 1
)

REM Compile LVGL draw
echo Compiling LVGL draw...
gcc %CFLAGS% %INCLUDES% -c lvgl/src/draw/*.c
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile LVGL draw
    pause
    exit /b 1
)

REM Compile LVGL draw sw
echo Compiling LVGL draw sw...
gcc %CFLAGS% %INCLUDES% -c lvgl/src/draw/sw/*.c
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile LVGL draw sw
    pause
    exit /b 1
)

REM Compile LVGL hal
echo Compiling LVGL hal...
gcc %CFLAGS% %INCLUDES% -c lvgl/src/hal/*.c
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile LVGL hal
    pause
    exit /b 1
)

REM Compile LVGL misc
echo Compiling LVGL misc...
gcc %CFLAGS% %INCLUDES% -c lvgl/src/misc/*.c
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile LVGL misc
    pause
    exit /b 1
)

REM Compile LVGL widgets
echo Compiling LVGL widgets...
gcc %CFLAGS% %INCLUDES% -c lvgl/src/widgets/*.c
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile LVGL widgets
    pause
    exit /b 1
)

REM Compile LVGL font
echo Compiling LVGL font...
gcc %CFLAGS% %INCLUDES% -c lvgl/src/font/*.c
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile LVGL font
    pause
    exit /b 1
)

REM Compile user files
echo Compiling user files...
gcc %CFLAGS% %INCLUDES% -c lvgl_user/*.c 2>nul

REM Compile main
echo Compiling main...
gcc %CFLAGS% %INCLUDES% -c simulator_main.c
if %errorlevel% neq 0 (
    echo ERROR: Failed to compile main
    pause
    exit /b 1
)

REM Link
echo Linking...
gcc *.o -o lvgl_simulator.exe %LIBS%
if %errorlevel% neq 0 (
    echo ERROR: Failed to link
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed!
echo.

if exist "lvgl_simulator.exe" (
    echo Executable created: lvgl_simulator.exe
    dir lvgl_simulator.exe
    echo.
    echo Starting simulator...
    echo Controls: Left click to touch, ESC to exit
    echo.
    start lvgl_simulator.exe
    echo Simulator started!
) else (
    echo ERROR: Executable not found
    pause
    exit /b 1
)

echo.
echo Done! You can run lvgl_simulator.exe directly next time.
pause
