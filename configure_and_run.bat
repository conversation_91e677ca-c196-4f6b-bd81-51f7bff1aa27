@echo off
chcp 65001 >nul
echo ========================================
echo       LVGL SDL2 Simulator Setup
echo ========================================
echo.

:: Set path variables
set "SDL2_DIR=C:\Users\<USER>\Desktop\mingW\SDL2-2.32.8\x86_64-w64-mingw32"
set "SDL2_BIN=%SDL2_DIR%\bin"
set "SDL2_LIB=%SDL2_DIR%\lib"
set "SDL2_INC=%SDL2_DIR%\include"
set "MINGW_BIN=C:\Users\<USER>\Desktop\mingW\mingw64\bin"

echo Checking environment...

:: Check MinGW
if not exist "%MINGW_BIN%\gcc.exe" (
    echo Error: MinGW compiler not found at: %MINGW_BIN%
    echo Please ensure MinG<PERSON> is correctly installed
    pause
    exit /b 1
)
echo Found MinGW compiler

:: Check SDL2
if not exist "%SDL2_BIN%\SDL2.dll" (
    echo Error: SDL2 not found at: %SDL2_BIN%
    echo Please check SDL2 installation
    pause
    exit /b 1
)
echo Found SDL2 library

:: Set environment variables (temporary)
echo Configuring environment variables...
set "PATH=%MINGW_BIN%;%SDL2_BIN%;%PATH%"
echo Environment variables configured

:: Verify tools
echo Verifying tools...
"%MINGW_BIN%\gcc.exe" --version | findstr gcc
if %errorlevel% neq 0 (
    echo Error: GCC verification failed
    pause
    exit /b 1
)
echo GCC verification successful

:: Check project files
echo Checking project files...
if not exist "simulator_main.c" (
    echo Error: Missing main file: simulator_main.c
    pause
    exit /b 1
)
if not exist "lvgl" (
    echo Error: Missing LVGL directory
    pause
    exit /b 1
)
echo Project files complete

:: Copy SDL2.dll to current directory
echo Preparing runtime libraries...
copy "%SDL2_BIN%\SDL2.dll" "." >nul 2>&1
if exist "SDL2.dll" (
    echo SDL2.dll copied to current directory
)

:: Configure LVGL
echo Configuring LVGL...
if exist "lv_conf_simulator.h" (
    if exist "lvgl\lv_conf.h" (
        copy "lvgl\lv_conf.h" "lvgl\lv_conf.h.backup" >nul 2>&1
    )
    copy "lv_conf_simulator.h" "lvgl\lv_conf.h" >nul 2>&1
    echo Using simulator configuration
)

:: Clean old files
echo Cleaning old files...
del /Q *.o lvgl_simulator.exe 2>nul

echo.
echo Starting compilation...
echo.

:: Compilation parameters
set "CFLAGS=-std=c99 -O2 -g -Wall -Wno-unused-parameter"
set "INCLUDES=-I./lvgl -I./ -I./lvgl_user -I./lvgl_user/ui_app -I%SDL2_INC%"
set "LIBS=-L%SDL2_LIB% -lmingw32 -lSDL2main -lSDL2 -lm"

:: Compile LVGL core
echo Compiling LVGL core...
for %%f in (lvgl\src\core\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Compilation failed: %%f
        pause
        exit /b 1
    )
)

echo Compiling LVGL draw...
for %%f in (lvgl\src\draw\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Compilation failed: %%f
        pause
        exit /b 1
    )
)

echo Compiling LVGL draw sw...
for %%f in (lvgl\src\draw\sw\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Compilation failed: %%f
        pause
        exit /b 1
    )
)

echo Compiling LVGL HAL...
for %%f in (lvgl\src\hal\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Compilation failed: %%f
        pause
        exit /b 1
    )
)

echo Compiling LVGL misc...
for %%f in (lvgl\src\misc\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Compilation failed: %%f
        pause
        exit /b 1
    )
)

echo Compiling LVGL widgets...
for %%f in (lvgl\src\widgets\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Compilation failed: %%f
        pause
        exit /b 1
    )
)

echo Compiling LVGL fonts...
for %%f in (lvgl\src\font\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Compilation failed: %%f
        pause
        exit /b 1
    )
)

echo Compiling LVGL extra...
if exist "lvgl\src\extra" (
    for %%f in (lvgl\src\extra\*.c) do (
        echo Compiling: %%f
        "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
        if %errorlevel% neq 0 (
            echo Compilation failed: %%f
            pause
            exit /b 1
        )
    )
)

echo Compiling LVGL extra themes...
if exist "lvgl\src\extra\themes" (
    for /r "lvgl\src\extra\themes" %%f in (*.c) do (
        echo Compiling: %%f
        "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
        if %errorlevel% neq 0 (
            echo Compilation failed: %%f
            pause
            exit /b 1
        )
    )
)

echo Compiling LVGL extra widgets...
if exist "lvgl\src\extra\widgets" (
    for /r "lvgl\src\extra\widgets" %%f in (*.c) do (
        echo Compiling: %%f
        "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
        if %errorlevel% neq 0 (
            echo Compilation failed: %%f
            pause
            exit /b 1
        )
    )
)

echo Compiling LVGL extra layouts...
if exist "lvgl\src\extra\layouts" (
    for /r "lvgl\src\extra\layouts" %%f in (*.c) do (
        echo Compiling: %%f
        "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
        if %errorlevel% neq 0 (
            echo Compilation failed: %%f
            pause
            exit /b 1
        )
    )
)

echo Compiling user files...
for %%f in (lvgl_user\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Warning: Skipped: %%f
    )
)

echo Compiling UI application...
for %%f in (lvgl_user\ui_app\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Warning: Skipped: %%f
    )
)

echo Compiling UI screens...
for %%f in (lvgl_user\ui_app\screens\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Warning: Skipped: %%f
    )
)

echo Compiling UI components...
for %%f in (lvgl_user\ui_app\components\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Warning: Skipped: %%f
    )
)

echo Compiling UI fonts...
for %%f in (lvgl_user\ui_app\fonts\*.c) do (
    echo Compiling: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo Warning: Skipped: %%f
    )
)

echo Compiling main program...
"%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "simulator_main.c" -o "simulator_main.o"
if %errorlevel% neq 0 (
    echo Main program compilation failed
    pause
    exit /b 1
)

echo Linking executable...
"%MINGW_BIN%\gcc.exe" *.o -o lvgl_simulator.exe %LIBS%
if %errorlevel% neq 0 (
    echo Linking failed
    pause
    exit /b 1
)

echo.
echo Compilation successful!
echo.

if exist "lvgl_simulator.exe" (
    echo Executable generated: lvgl_simulator.exe
    dir lvgl_simulator.exe
    echo.
    echo Starting simulator...
    echo Operation instructions:
    echo    - Left mouse button: simulate touch
    echo    - Mouse drag: simulate swipe
    echo    - ESC key: exit
    echo.
    
    start lvgl_simulator.exe
    echo Simulator started!
) else (
    echo Executable not found
    pause
    exit /b 1
)

echo.
echo Configuration complete!
echo Next time you can run directly: lvgl_simulator.exe
pause
