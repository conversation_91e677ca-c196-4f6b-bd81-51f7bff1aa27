/*******************************************************************************
 * Size: 25 px
 * Bpp: 2
 * Opts: --bpp 2 --size 25 --font D:\Work\LVGL\LYwatch\assets\思源黑体TW-Heavy.otf -o D:\Work\LVGL\LYwatch\assets\ui_font_pjfont25.c --format lvgl --symbols 0123456789-.uSvmbp%/h --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_PJFONT25
#define UI_FONT_PJFONT25 1
#endif

#if UI_FONT_PJFONT25

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0025 "%" */
    0x2, 0xfd, 0x0, 0x0, 0xf4, 0x0, 0xf, 0xff,
    0x80, 0x2, 0xf0, 0x0, 0x2f, 0xef, 0xd0, 0x3,
    0xd0, 0x0, 0x3f, 0x7, 0xf0, 0xf, 0xc0, 0x0,
    0x3f, 0x3, 0xf0, 0x1f, 0x0, 0x0, 0x7f, 0x3,
    0xf0, 0x3e, 0x0, 0x0, 0x3f, 0x3, 0xf0, 0x7c,
    0x0, 0x0, 0x3f, 0x3, 0xf0, 0xf8, 0x2f, 0x90,
    0x3f, 0x8b, 0xe1, 0xf0, 0xff, 0xf4, 0xf, 0xff,
    0xc3, 0xe2, 0xfe, 0xfc, 0x7, 0xff, 0x7, 0xc3,
    0xf0, 0xbe, 0x0, 0x54, 0xf, 0x83, 0xf0, 0x7e,
    0x0, 0x0, 0x2f, 0x7, 0xe0, 0x3f, 0x0, 0x0,
    0x3d, 0x7, 0xe0, 0x3f, 0x0, 0x0, 0xbc, 0x3,
    0xf0, 0x7e, 0x0, 0x0, 0xf4, 0x3, 0xf0, 0xbe,
    0x0, 0x2, 0xf0, 0x2, 0xfe, 0xfc, 0x0, 0x3,
    0xd0, 0x0, 0xff, 0xf4, 0x0, 0xb, 0xc0, 0x0,
    0x2f, 0x90,

    /* U+002D "-" */
    0x0, 0x0, 0xbf, 0xfd, 0xbf, 0xfd, 0xbf, 0xfd,

    /* U+002E "." */
    0x6, 0x40, 0xbf, 0x83, 0xff, 0xf, 0xfc, 0x3f,
    0xe0, 0x3e, 0x0,

    /* U+002F "/" */
    0x0, 0x5, 0x40, 0x7, 0xe0, 0x2, 0xf4, 0x0,
    0xbc, 0x0, 0x3f, 0x0, 0xf, 0x80, 0x7, 0xd0,
    0x2, 0xf4, 0x0, 0xfc, 0x0, 0x3f, 0x0, 0x1f,
    0x80, 0x7, 0xd0, 0x2, 0xf0, 0x0, 0xfc, 0x0,
    0x3f, 0x0, 0x1f, 0x80, 0xb, 0xd0, 0x3, 0xf0,
    0x0, 0xfc, 0x0, 0x3e, 0x0, 0x1f, 0x40, 0xb,
    0xc0, 0x3, 0xf0, 0x0, 0xfc, 0x0, 0x7e, 0x0,
    0x2f, 0x40, 0x0,

    /* U+0030 "0" */
    0x0, 0xbf, 0x90, 0x0, 0x7f, 0xff, 0x80, 0xf,
    0xff, 0xfd, 0x3, 0xff, 0xbf, 0xf0, 0x3f, 0xc0,
    0xff, 0x4b, 0xf8, 0x7, 0xfc, 0xbf, 0x40, 0x3f,
    0xcf, 0xf4, 0x3, 0xfc, 0xff, 0x0, 0x3f, 0xcf,
    0xf0, 0x3, 0xfc, 0xff, 0x0, 0x3f, 0xcf, 0xf4,
    0x3, 0xfc, 0xbf, 0x40, 0x3f, 0xc7, 0xf8, 0x7,
    0xf8, 0x3f, 0xc0, 0xff, 0x42, 0xff, 0xbf, 0xf0,
    0xf, 0xff, 0xfd, 0x0, 0x7f, 0xff, 0x80, 0x0,
    0xbf, 0x80, 0x0,

    /* U+0031 "1" */
    0x0, 0x2f, 0xd0, 0x2, 0xff, 0xf4, 0x2, 0xff,
    0xfd, 0x0, 0xbf, 0xff, 0x40, 0x1a, 0xbf, 0xd0,
    0x0, 0xf, 0xf4, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0xff, 0x40, 0x0, 0x3f, 0xd0, 0x0, 0xf, 0xf4,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0xff, 0x40, 0x0,
    0x3f, 0xd0, 0x0, 0xf, 0xf4, 0x0, 0x3, 0xfd,
    0x0, 0xaa, 0xff, 0xe9, 0x3f, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xe3, 0xff, 0xff, 0xf8,

    /* U+0032 "2" */
    0x0, 0xbf, 0xa0, 0x0, 0xbf, 0xff, 0xd0, 0x2f,
    0xff, 0xff, 0x2, 0xff, 0xbf, 0xf8, 0xb, 0x40,
    0xbf, 0xc0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x3f,
    0xc0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0xff, 0x40,
    0x0, 0x2f, 0xf0, 0x0, 0x3, 0xfd, 0x0, 0x0,
    0xff, 0xc0, 0x0, 0x3f, 0xf0, 0x0, 0xf, 0xfd,
    0x0, 0x3, 0xff, 0x40, 0x0, 0xff, 0xfb, 0xff,
    0x3f, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0x3f,
    0xff, 0xff, 0xf0,

    /* U+0033 "3" */
    0x0, 0xbf, 0xe0, 0x0, 0xbf, 0xff, 0xe0, 0x2f,
    0xff, 0xff, 0x80, 0xff, 0xbf, 0xfc, 0x2, 0x0,
    0x7f, 0xc0, 0x0, 0x7, 0xfc, 0x0, 0x1, 0xff,
    0x80, 0x7, 0xff, 0xe0, 0x0, 0x7f, 0xf8, 0x0,
    0x7, 0xff, 0xf0, 0x0, 0x16, 0xff, 0xc0, 0x0,
    0x3, 0xfe, 0x0, 0x0, 0x2f, 0xe0, 0x40, 0x2,
    0xff, 0xf, 0x40, 0x7f, 0xe3, 0xff, 0xbf, 0xfd,
    0x3f, 0xff, 0xff, 0x80, 0xff, 0xff, 0xe0, 0x1,
    0xbf, 0xe0, 0x0,

    /* U+0034 "4" */
    0x0, 0x7, 0xff, 0x80, 0x0, 0x3f, 0xfe, 0x0,
    0x1, 0xff, 0xf8, 0x0, 0xf, 0xff, 0xe0, 0x0,
    0xbf, 0xbf, 0x80, 0x7, 0xfa, 0xfe, 0x0, 0x3f,
    0xcb, 0xf8, 0x1, 0xfe, 0x2f, 0xe0, 0xf, 0xf0,
    0xbf, 0x80, 0xbf, 0x42, 0xfe, 0x3, 0xfd, 0x5f,
    0xfd, 0x5f, 0xff, 0xff, 0xfe, 0x7f, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xe0, 0x0, 0xb, 0xf8,
    0x0, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x2, 0xfe, 0x0, 0x0, 0xb, 0xf8, 0x0,

    /* U+0035 "5" */
    0x7, 0xff, 0xff, 0xc0, 0xbf, 0xff, 0xfc, 0xb,
    0xff, 0xff, 0xc0, 0xbf, 0xff, 0xfc, 0xb, 0xf4,
    0x0, 0x0, 0xbf, 0x0, 0x0, 0xf, 0xfb, 0xf4,
    0x0, 0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xc0,
    0x3d, 0x1f, 0xfd, 0x0, 0x0, 0x3f, 0xe0, 0x0,
    0x2, 0xfe, 0x0, 0x0, 0x2f, 0xf0, 0x40, 0x3,
    0xfe, 0xf, 0x0, 0x7f, 0xd3, 0xff, 0xbf, 0xfc,
    0x3f, 0xff, 0xff, 0x40, 0xff, 0xff, 0xd0, 0x1,
    0xbf, 0xe0, 0x0,

    /* U+0036 "6" */
    0x0, 0x6f, 0xe4, 0x0, 0x2f, 0xff, 0xe0, 0xb,
    0xff, 0xff, 0x81, 0xff, 0xef, 0xf0, 0x3f, 0xe0,
    0x8, 0x7, 0xfc, 0x0, 0x0, 0x7f, 0x80, 0x0,
    0xb, 0xf5, 0xff, 0x80, 0xff, 0xff, 0xff, 0xf,
    0xff, 0xff, 0xf4, 0xff, 0xd0, 0xbf, 0xcb, 0xf8,
    0x3, 0xfc, 0xbf, 0x40, 0x3f, 0xc7, 0xf8, 0x3,
    0xfc, 0x3f, 0xc0, 0x7f, 0xc2, 0xff, 0xaf, 0xf8,
    0xf, 0xff, 0xff, 0x0, 0x3f, 0xff, 0xc0, 0x0,
    0x7f, 0x90, 0x0,

    /* U+0037 "7" */
    0xbf, 0xff, 0xff, 0xcb, 0xff, 0xff, 0xfc, 0xbf,
    0xff, 0xff, 0xcb, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0xfe, 0x0, 0x0, 0x2f, 0xc0, 0x0, 0x7, 0xf4,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x1f, 0xe0, 0x0,
    0x2, 0xfc, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x7,
    0xf8, 0x0, 0x0, 0xbf, 0x80, 0x0, 0xf, 0xf4,
    0x0, 0x0, 0xff, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x1,
    0xff, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0xbf, 0x90, 0x0, 0xbf, 0xff, 0xc0, 0x1f,
    0xff, 0xfe, 0x3, 0xfe, 0x1f, 0xf0, 0x3f, 0xc0,
    0x7f, 0x3, 0xfc, 0x7, 0xf0, 0x3f, 0xc0, 0x7f,
    0x1, 0xff, 0x4f, 0xd0, 0xb, 0xff, 0xf4, 0x0,
    0x3f, 0xff, 0x80, 0xf, 0xff, 0xfe, 0x3, 0xf8,
    0x2f, 0xf4, 0xbf, 0x0, 0xbf, 0xcb, 0xf0, 0x3,
    0xfc, 0xbf, 0x40, 0x3f, 0xc7, 0xfd, 0x1f, 0xf8,
    0x3f, 0xff, 0xff, 0x0, 0xff, 0xff, 0xd0, 0x1,
    0xbf, 0x90, 0x0,

    /* U+0039 "9" */
    0x0, 0x6f, 0xe0, 0x0, 0xf, 0xff, 0xf0, 0x0,
    0xff, 0xff, 0xf0, 0x7, 0xfe, 0x7f, 0xf0, 0x2f,
    0xd0, 0x3f, 0xc0, 0xff, 0x0, 0xbf, 0x83, 0xfc,
    0x1, 0xff, 0xf, 0xf4, 0xb, 0xfc, 0x2f, 0xf5,
    0xbf, 0xf0, 0x3f, 0xff, 0xff, 0xc0, 0x7f, 0xfe,
    0xff, 0x0, 0x2f, 0x97, 0xfc, 0x0, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0xff, 0x40, 0x24, 0xb, 0xfc,
    0x2, 0xff, 0xff, 0xe0, 0x2f, 0xff, 0xff, 0x0,
    0x2f, 0xff, 0xf0, 0x0, 0x1b, 0xf9, 0x0, 0x0,

    /* U+0053 "S" */
    0x0, 0x1b, 0xf9, 0x0, 0x1, 0xff, 0xff, 0xd0,
    0x3, 0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xe0,
    0xf, 0xf8, 0x7, 0xc0, 0x1f, 0xf4, 0x0, 0x0,
    0xf, 0xfc, 0x0, 0x0, 0xf, 0xff, 0x90, 0x0,
    0x7, 0xff, 0xfe, 0x0, 0x1, 0xff, 0xff, 0xd0,
    0x0, 0x1f, 0xff, 0xf0, 0x0, 0x1, 0xbf, 0xf8,
    0x0, 0x0, 0xf, 0xfc, 0x2, 0x0, 0xb, 0xfc,
    0xb, 0xe0, 0xf, 0xfc, 0x1f, 0xff, 0xff, 0xf4,
    0x2f, 0xff, 0xff, 0xf0, 0x7, 0xff, 0xff, 0x80,
    0x0, 0x6f, 0xf8, 0x0,

    /* U+0062 "b" */
    0x2a, 0x80, 0x0, 0x0, 0xff, 0x0, 0x0, 0x3,
    0xfc, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x3f,
    0xc0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x3, 0xfc,
    0x6f, 0x80, 0xf, 0xff, 0xff, 0xd0, 0x3f, 0xff,
    0xff, 0xc0, 0xff, 0xff, 0xff, 0x83, 0xfe, 0x3,
    0xff, 0xf, 0xf0, 0x7, 0xfc, 0x3f, 0xc0, 0x1f,
    0xf4, 0xff, 0x0, 0x3f, 0xd3, 0xfc, 0x1, 0xff,
    0x4f, 0xf0, 0xb, 0xfc, 0x3f, 0xd0, 0x3f, 0xf0,
    0xff, 0xff, 0xff, 0x43, 0xff, 0xff, 0xfc, 0xf,
    0xef, 0xff, 0xc0, 0x3f, 0x47, 0xf4, 0x0,

    /* U+0068 "h" */
    0x2a, 0x80, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x3f,
    0xc0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x3f, 0xc0,
    0x0, 0x3, 0xfc, 0x0, 0x0, 0x3f, 0xc2, 0xfd,
    0x3, 0xfd, 0xff, 0xf8, 0x3f, 0xff, 0xff, 0xc3,
    0xff, 0xff, 0xfe, 0x3f, 0xf4, 0x3f, 0xe3, 0xfd,
    0x2, 0xfe, 0x3f, 0xc0, 0x2f, 0xf3, 0xfc, 0x2,
    0xff, 0x3f, 0xc0, 0x2f, 0xf3, 0xfc, 0x2, 0xff,
    0x3f, 0xc0, 0x2f, 0xf3, 0xfc, 0x2, 0xff, 0x3f,
    0xc0, 0x2f, 0xf3, 0xfc, 0x2, 0xff, 0x3f, 0xc0,
    0x2f, 0xf0,

    /* U+006D "m" */
    0x3f, 0x82, 0xfd, 0x2, 0xfd, 0x3, 0xf9, 0xff,
    0xf8, 0xff, 0xf8, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x3f, 0xf4,
    0x7f, 0xf4, 0x3f, 0xf3, 0xfd, 0x3, 0xfe, 0x2,
    0xff, 0x3f, 0xc0, 0x3f, 0xe0, 0x2f, 0xf3, 0xfc,
    0x3, 0xfe, 0x1, 0xff, 0x3f, 0xc0, 0x3f, 0xe0,
    0x1f, 0xf3, 0xfc, 0x3, 0xfe, 0x1, 0xff, 0x3f,
    0xc0, 0x3f, 0xe0, 0x1f, 0xf3, 0xfc, 0x3, 0xfe,
    0x1, 0xff, 0x3f, 0xc0, 0x3f, 0xe0, 0x1f, 0xf3,
    0xfc, 0x3, 0xfe, 0x1, 0xff, 0x3f, 0xc0, 0x3f,
    0xe0, 0x1f, 0xf0,

    /* U+0070 "p" */
    0x3f, 0x86, 0xf8, 0x0, 0xff, 0xff, 0xfd, 0x3,
    0xff, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xf8, 0x3f,
    0xe0, 0x3f, 0xf0, 0xff, 0x0, 0x7f, 0xc3, 0xfc,
    0x1, 0xff, 0x4f, 0xf0, 0x3, 0xfd, 0x3f, 0xc0,
    0x1f, 0xf4, 0xff, 0x0, 0xbf, 0xc3, 0xfe, 0x3,
    0xff, 0xf, 0xff, 0xff, 0xf4, 0x3f, 0xff, 0xff,
    0xc0, 0xff, 0xff, 0xfc, 0x3, 0xfc, 0x7f, 0x40,
    0xf, 0xf0, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0,

    /* U+0075 "u" */
    0x7f, 0xc0, 0x3f, 0xe7, 0xfc, 0x3, 0xfe, 0x7f,
    0xc0, 0x3f, 0xe7, 0xfc, 0x3, 0xfe, 0x7f, 0xc0,
    0x3f, 0xe7, 0xfc, 0x3, 0xfe, 0x7f, 0xc0, 0x3f,
    0xe7, 0xfc, 0x3, 0xfe, 0x7f, 0xc0, 0x3f, 0xe7,
    0xfc, 0x3, 0xfe, 0x3f, 0xe0, 0xbf, 0xe3, 0xff,
    0xff, 0xfe, 0x2f, 0xff, 0xff, 0xe0, 0xff, 0xf9,
    0xfe, 0x2, 0xfd, 0xf, 0xe0,

    /* U+0076 "v" */
    0x7f, 0xc0, 0xb, 0xf8, 0xff, 0x0, 0x3f, 0xd3,
    0xfd, 0x0, 0xff, 0xb, 0xf8, 0x7, 0xfc, 0xf,
    0xf0, 0x2f, 0xd0, 0x3f, 0xc0, 0xff, 0x0, 0xbf,
    0x43, 0xfc, 0x1, 0xfe, 0x1f, 0xe0, 0x3, 0xfc,
    0xbf, 0x0, 0xb, 0xf3, 0xfc, 0x0, 0x1f, 0xdf,
    0xe0, 0x0, 0x3f, 0xff, 0x40, 0x0, 0xbf, 0xfc,
    0x0, 0x1, 0xff, 0xe0, 0x0, 0x3, 0xff, 0x40,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 394, .box_w = 24, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 114, .adv_w = 153, .box_w = 8, .box_h = 4, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 122, .adv_w = 140, .box_w = 7, .box_h = 6, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 133, .adv_w = 154, .box_w = 9, .box_h = 26, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 192, .adv_w = 244, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 259, .adv_w = 244, .box_w = 13, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 321, .adv_w = 244, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 388, .adv_w = 244, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 455, .adv_w = 244, .box_w = 15, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 527, .adv_w = 244, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 594, .adv_w = 244, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 661, .adv_w = 244, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 728, .adv_w = 244, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 795, .adv_w = 244, .box_w = 15, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 867, .adv_w = 256, .box_w = 16, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 943, .adv_w = 263, .box_w = 15, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1022, .adv_w = 263, .box_w = 14, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1096, .adv_w = 394, .box_w = 22, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1179, .adv_w = 263, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 1254, .adv_w = 262, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1307, .adv_w = 243, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x8, 0x9, 0xa, 0xb, 0xc, 0xd, 0xe,
    0xf, 0x10, 0x11, 0x12, 0x13, 0x14, 0x2e, 0x3d,
    0x43, 0x48, 0x4b, 0x50, 0x51
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 37, .range_length = 82, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 21, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    2, 15,
    2, 21,
    3, 21,
    4, 20,
    15, 2,
    15, 15,
    16, 3,
    16, 21,
    19, 3,
    19, 21,
    21, 2,
    21, 3,
    21, 4
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -13, -8, -29, -9, 9, -9, -5, -4,
    -5, -4, -8, -29, -9
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 13,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 2,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t ui_font_pjfont25 = {
#else
lv_font_t ui_font_pjfont25 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 26,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if UI_FONT_PJFONT25*/

