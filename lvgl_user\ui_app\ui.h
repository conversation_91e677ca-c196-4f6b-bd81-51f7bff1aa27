// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.3.1
// LVGL version: 8.3.6
// Project name: LYwatch

#ifndef _LYWATCH_UI_H
#define _LYWATCH_UI_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

#include "ui_helpers.h"
#include "ui_events.h"

// SCREEN: ui_StartScreen
void ui_StartScreen_screen_init(void);
void ui_event_StartScreen(lv_event_t * e);
extern lv_obj_t * ui_StartScreen;
extern lv_obj_t * ui_HelloLable;
// SCREEN: ui_HomeScreen
void ui_HomeScreen_screen_init(void);
void ui_event_HomeScreen(lv_event_t * e);
extern lv_obj_t * ui_HomeScreen;
extern lv_obj_t * ui_TimePanel;
extern lv_obj_t * ui_HourLable;
extern lv_obj_t * ui_MinuLable;
extern lv_obj_t * ui_DateLable;
extern lv_obj_t * ui_Dot1Panle;
extern lv_obj_t * ui_Dot2Panle;
extern lv_obj_t * ui_MainDataPanel;
extern lv_obj_t * ui_HeartArc;
extern lv_obj_t * ui_SaO2Arc;
extern lv_obj_t * ui_BatArc;
extern lv_obj_t * ui_Label7;
extern lv_obj_t * ui_SaO2Lable;
extern lv_obj_t * ui_HeartLable;
extern lv_obj_t * ui_Label2;
extern lv_obj_t * ui_Label1;
extern lv_obj_t * ui_BatLable;
extern lv_obj_t * ui_DoseRatePanel;
extern lv_obj_t * ui_Label3;
extern lv_obj_t * ui_DoseRateLable;
extern lv_obj_t * ui_Label4;
// SCREEN: ui_SysSetingScreen
void ui_SysSetingScreen_screen_init(void);
void ui_event_SysSetingScreen(lv_event_t * e);
extern lv_obj_t * ui_SysSetingScreen;
extern lv_obj_t * ui_SetingPanle;
extern lv_obj_t * ui_Label5;
extern lv_obj_t * ui_Label6;
extern lv_obj_t * ui_BTSwitch;
extern lv_obj_t * ui_FlyModeSwitch;
extern lv_obj_t * ui_GpsSwitch;
extern lv_obj_t * ui_SendDataNowSwitch;
extern lv_obj_t * ui_AutoSendSwitch;
extern lv_obj_t * ui_CSQLabel;
extern lv_obj_t * ui_GPSNumLabel;
extern lv_obj_t * ui_UpSendLable;
extern lv_obj_t * ui____initial_actions0;

LV_FONT_DECLARE(ui_font_icon40);
LV_FONT_DECLARE(ui_font_pjfont25);
LV_FONT_DECLARE(ui_font_pjfont40);
LV_FONT_DECLARE(ui_font_pjfont70);
LV_FONT_DECLARE(ui_font_pjXiFont40);
LV_FONT_DECLARE(ui_font_pjXiFont20);
LV_FONT_DECLARE(ui_font_pjXiFont30);

void ui_init(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
