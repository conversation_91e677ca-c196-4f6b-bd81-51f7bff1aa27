// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.3.1
// LVGL version: 8.3.6
// Project name: LYwatch

#include "../ui.h"
#include "../../system_set.h"
uint8_t start_screen_load_state = 0;//开始屏幕画面状态：0应

void ui_StartScreen_screen_init(void)
{
    ui_StartScreen = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_StartScreen, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_HelloLable = lv_label_create(ui_StartScreen);
    lv_obj_set_width(ui_HelloLable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_HelloLable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_HelloLable, LV_ALIGN_CENTER);

    lv_label_set_text(ui_HelloLable, "Hello");
    if(system_state.watch_power_state == 4){
        lv_label_set_text(ui_HelloLable, "LYwatch");
    }   
    lv_obj_set_style_text_letter_space(ui_HelloLable, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui_HelloLable, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_HelloLable, &ui_font_pjfont70, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(ui_StartScreen, ui_event_StartScreen, LV_EVENT_ALL, NULL);

}
