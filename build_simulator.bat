@echo off
chcp 65001 >nul
echo ========================================
echo       LVGL手表模拟器构建脚本 (Windows)
echo ========================================
echo.

:: 检查是否安装了编译器
:check_compiler
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到GCC编译器
    echo.
    echo 请选择以下方式安装:
    echo.
    echo 方式1: 安装MinGW-w64 + MSYS2 ^(推荐^)
    echo   1. 下载安装 MSYS2: https://www.msys2.org/
    echo   2. 在MSYS2终端中运行:
    echo      pacman -S mingw-w64-x86_64-gcc
    echo      pacman -S mingw-w64-x86_64-SDL2
    echo      pacman -S make
    echo.
    echo 方式2: 安装TDM-GCC
    echo   下载: https://jmeubank.github.io/tdm-gcc/
    echo.
    echo 方式3: 使用Visual Studio
    echo   安装Visual Studio 2019/2022 Community版本
    echo.
    pause
    exit /b 1
)

:: 检查SDL2
:check_sdl2
echo 🔍 检查SDL2库...
pkg-config --exists sdl2 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到SDL2库
    echo.
    echo 请安装SDL2开发库:
    echo.
    echo 如果使用MSYS2:
    echo   pacman -S mingw-w64-x86_64-SDL2
    echo.
    echo 如果使用其他编译器:
    echo   1. 下载SDL2开发库: https://www.libsdl.org/download-2.0.php
    echo   2. 解压到 C:\SDL2 目录
    echo   3. 将 C:\SDL2\bin 添加到系统PATH
    echo.
    pause
    exit /b 1
) else (
    echo ✅ SDL2已安装
)

:: 检查必需文件
:check_files
echo 🔍 检查项目文件...
if not exist "simulator_main.c" (
    echo ❌ 缺少 simulator_main.c 文件
    pause
    exit /b 1
)
if not exist "lvgl" (
    echo ❌ 缺少 lvgl 目录
    pause
    exit /b 1
)
if not exist "lvgl_user" (
    echo ❌ 缺少 lvgl_user 目录
    pause
    exit /b 1
)
echo ✅ 项目文件检查完毕

:: 创建简化的Makefile
:create_makefile
echo 📝 创建构建文件...
(
echo # 简化的LVGL模拟器Makefile ^(Windows^)
echo.
echo CC = gcc
echo CFLAGS = -std=c99 -Wall -Wextra -O2 -g
echo CFLAGS += -I./lvgl -I./ -I./lvgl_user
echo.
echo # 尝试使用pkg-config，如果失败则使用默认路径
echo ifeq ^(^$^(shell pkg-config --exists sdl2 ^&^& echo yes^),yes^)
echo     CFLAGS += ^$^(shell pkg-config --cflags sdl2^)
echo     LDFLAGS = ^$^(shell pkg-config --libs sdl2^) -lm
echo else
echo     CFLAGS += -IC:/SDL2/include
echo     LDFLAGS = -LC:/SDL2/lib -lSDL2main -lSDL2 -lm
echo endif
echo.
echo # LVGL核心源文件
echo LVGL_CORE := ^$^(wildcard lvgl/src/core/*.c^)
echo LVGL_DRAW := ^$^(wildcard lvgl/src/draw/*.c^)
echo LVGL_DRAW_SW := ^$^(wildcard lvgl/src/draw/sw/*.c^)
echo LVGL_HAL := ^$^(wildcard lvgl/src/hal/*.c^)
echo LVGL_MISC := ^$^(wildcard lvgl/src/misc/*.c^)
echo LVGL_WIDGETS := ^$^(wildcard lvgl/src/widgets/*.c^)
echo LVGL_FONT := ^$^(wildcard lvgl/src/font/*.c^)
echo.
echo # LVGL额外组件
echo LVGL_EXTRA_WIDGETS := ^$^(wildcard lvgl/src/extra/widgets/*/*.c^)
echo LVGL_EXTRA_THEMES := ^$^(wildcard lvgl/src/extra/themes/*/*.c^)
echo LVGL_EXTRA_LAYOUTS := ^$^(wildcard lvgl/src/extra/layouts/*/*.c^)
echo.
echo # UI应用源文件
echo UI_SOURCES := ^$^(wildcard lvgl_user/ui_app/*.c^)
echo UI_SOURCES += ^$^(wildcard lvgl_user/ui_app/screens/*.c^)
echo UI_SOURCES += ^$^(wildcard lvgl_user/ui_app/components/*.c^)
echo UI_SOURCES += ^$^(wildcard lvgl_user/ui_app/fonts/*.c^)
echo.
echo # 主模拟器文件
echo MAIN_SRC = simulator_main.c
echo.
echo # 所有源文件
echo SOURCES = ^$^(LVGL_CORE^) ^$^(LVGL_DRAW^) ^$^(LVGL_DRAW_SW^) ^$^(LVGL_HAL^) ^$^(LVGL_MISC^)
echo SOURCES += ^$^(LVGL_WIDGETS^) ^$^(LVGL_FONT^) ^$^(LVGL_EXTRA_WIDGETS^) ^$^(LVGL_EXTRA_THEMES^) ^$^(LVGL_EXTRA_LAYOUTS^)
echo SOURCES += ^$^(UI_SOURCES^) ^$^(MAIN_SRC^)
echo.
echo # 目标文件
echo OBJECTS = ^$^(SOURCES:.c=.o^)
echo.
echo # 可执行文件名
echo TARGET = lvgl_simulator.exe
echo.
echo .PHONY: all clean run
echo.
echo all: ^$^(TARGET^)
echo 	@echo ✅ 构建完成! 运行: ./^$^(TARGET^)
echo.
echo ^$^(TARGET^): ^$^(OBJECTS^)
echo 	@echo 🔗 链接可执行文件...
echo 	^$^(CC^) ^$^(OBJECTS^) -o ^$@ ^$^(LDFLAGS^)
echo.
echo %%.o: %%.c
echo 	@echo 📝 编译: ^$^<
echo 	^$^(CC^) ^$^(CFLAGS^) -c ^$^< -o ^$@
echo.
echo clean:
echo 	@echo 🧹 清理构建文件...
echo 	del /Q ^$^(TARGET^) 2^>nul ^|^| true
echo 	for /r . %%i in ^(*.o^) do del "%%i" 2^>nul ^|^| true
echo.
echo run: ^$^(TARGET^)
echo 	@echo 🚀 启动模拟器...
echo 	./^$^(TARGET^)
) > Makefile.simple

:: 使用模拟器配置
:config_simulator
echo ⚙️ 配置模拟器...
if exist "lvgl\lv_conf.h" (
    copy "lvgl\lv_conf.h" "lvgl\lv_conf.h.backup" >nul
    echo ✅ 备份了原配置文件
)

if exist "lv_conf_simulator.h" (
    copy "lv_conf_simulator.h" "lvgl\lv_conf.h" >nul
    echo ✅ 使用模拟器配置文件
)

:: 开始构建
:build
echo.
echo 🏗️ 开始构建...
make -f Makefile.simple clean 2>nul
make -f Makefile.simple

if %errorlevel% equ 0 (
    echo.
    echo 🎉 构建成功!
    echo 🚀 运行模拟器: lvgl_simulator.exe
    echo 💡 提示: 鼠标左键模拟触摸操作
    echo.
    echo 按任意键运行模拟器...
    pause >nul
    lvgl_simulator.exe
) else (
    echo.
    echo ❌ 构建失败，请检查错误信息
    pause
    exit /b 1
)

:end
echo.
echo 按任意键退出...
pause >nul 