/*******************************************************************************
 * Size: 70 px
 * Bpp: 2
 * Opts: --bpp 2 --size 70 --font D:\Work\LVGL\LYwatch\assets\思源黑体TW-Heavy.otf -o D:\Work\LVGL\LYwatch\assets\ui_font_pjfont70.c --format lvgl --symbols HelloLYwatch0123456789:- --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_PJFONT70
#define UI_FONT_PJFONT70 1
#endif

#if UI_FONT_PJFONT70

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+002D "-" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xd7,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x7f, 0xff, 0xff, 0xff, 0xff, 0x40,

    /* U+0030 "0" */
    0x0, 0x0, 0x0, 0x6, 0xbf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xab, 0xff, 0xff, 0xff, 0x40, 0x0,
    0xff, 0xff, 0xff, 0x40, 0xb, 0xff, 0xff, 0xfc,
    0x0, 0x2f, 0xff, 0xff, 0xd0, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x3, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xff, 0xff, 0xfe, 0x0, 0x7f, 0xff, 0xff, 0x40,
    0x0, 0xb, 0xff, 0xff, 0xf0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x40, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf8,
    0xf, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xfc, 0x1f, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xc2, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xfc, 0x3f, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xd3,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xfd, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xe3, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0x80,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xe3, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfe, 0x3f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xe3, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfe, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xe3, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xf, 0xff, 0xff, 0xe3, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfe,
    0x3f, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xd2, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xfd, 0x2f, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xc1, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xfc, 0xf, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xc0,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xf8, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x40, 0x7f, 0xff, 0xff, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf0, 0x3, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x0, 0x1f, 0xff, 0xff, 0xd0, 0x1,
    0xff, 0xff, 0xfe, 0x0, 0x3, 0xff, 0xff, 0xfc,
    0x0, 0xf, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x7f, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2b, 0xff, 0x90, 0x0, 0x0,
    0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x0, 0x5, 0x55, 0x55, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0,

    /* U+0032 "2" */
    0x0, 0x0, 0x0, 0x6b, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x7,
    0xff, 0xff, 0x80, 0x2, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x1f, 0xff, 0x80, 0x0, 0x7, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x40, 0x0, 0x1, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xfe, 0x1, 0x5a, 0xaa, 0xaa,
    0x80, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+0033 "3" */
    0x0, 0x0, 0x0, 0x1a, 0xff, 0xf9, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x2f, 0xff, 0xe4, 0x0, 0x7f, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0xbf, 0xe0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xf0, 0x0, 0x3, 0xf4, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xc, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xbf, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xf0, 0x0, 0xd0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xfe, 0x0, 0x3f, 0x40, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xe0, 0xb, 0xfe,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xfd, 0x1,
    0xff, 0xfe, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xd0, 0x3f, 0xff, 0xff, 0x50, 0x5b, 0xff, 0xff,
    0xff, 0xfc, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xbf, 0xff, 0xa4, 0x0, 0x0,
    0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x55, 0x55, 0x55,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xd3,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xc7, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x87, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0x7, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfe, 0x7,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xfc, 0x7, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xf8, 0x7, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xf0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xe0, 0xb,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xc0, 0xb, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x40, 0xb, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xff, 0xff, 0xfd, 0x0, 0xb,
    0xff, 0xff, 0xf0, 0x0, 0x2, 0xff, 0xff, 0xf8,
    0x0, 0xb, 0xff, 0xff, 0xf0, 0x0, 0x3, 0xff,
    0xff, 0xf0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0,
    0xf, 0xff, 0xff, 0xe0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x1f, 0xff, 0xff, 0xc0, 0x0, 0xb,
    0xff, 0xff, 0xf0, 0x0, 0x3f, 0xff, 0xff, 0xd5,
    0x55, 0x5f, 0xff, 0xff, 0xf9, 0x55, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0,

    /* U+0035 "5" */
    0x0, 0x5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x54, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x1, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xf5, 0xbf, 0xfe, 0x40, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0xbf, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x1, 0xff, 0xe0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x2, 0xe0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xd0, 0x0, 0xc0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfd, 0x0, 0x2f, 0x40, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xc0, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xfc, 0x0,
    0xff, 0xfe, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0x40, 0x3f, 0xff, 0xfe, 0x50, 0x1b, 0xff, 0xff,
    0xff, 0xf0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0x90, 0x0, 0x0,
    0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x0, 0x6b, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0x90, 0xb, 0xff, 0xf4, 0x0, 0xb, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x1f, 0xf4, 0x0, 0x3,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0xb8, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x4,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0x0, 0x6, 0xff, 0xe4,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xc0, 0x2f, 0xff,
    0xff, 0xe0, 0x0, 0x7, 0xff, 0xff, 0xf0, 0x7f,
    0xff, 0xff, 0xff, 0x80, 0x2, 0xff, 0xff, 0xfc,
    0xbf, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xbf, 0xff,
    0xfe, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x43, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x2f, 0xff, 0xff, 0xff,
    0xd0, 0x2, 0xff, 0xff, 0xff, 0xcb, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x1f, 0xff, 0xff, 0xf2, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x2, 0xff, 0xff, 0xfd,
    0xbf, 0xff, 0xff, 0x80, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x9f, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xe7, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x3f, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xcb, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xe1,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xf8, 0x3f, 0xff, 0xff, 0x40, 0x0, 0x0, 0xff,
    0xff, 0xfe, 0xb, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0x41, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff,
    0xc0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x7, 0xff,
    0xff, 0xfc, 0x0, 0x1f, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xe5, 0xbf, 0xff, 0xff, 0xfc,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfe,
    0x90, 0x0, 0x0, 0x0,

    /* U+0037 "7" */
    0x15, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x47, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x0, 0x1b, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0xff, 0xff, 0xff, 0x40,
    0xb, 0xff, 0xff, 0xf8, 0x0, 0x7f, 0xff, 0xff,
    0x40, 0x0, 0x7f, 0xff, 0xff, 0x0, 0x2f, 0xff,
    0xff, 0x80, 0x0, 0xb, 0xff, 0xff, 0xc0, 0xb,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0xff, 0xff, 0xf0,
    0x3, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x3f, 0xff,
    0xfc, 0x0, 0xff, 0xff, 0xfc, 0x0, 0x0, 0xf,
    0xff, 0xff, 0x0, 0x2f, 0xff, 0xff, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xc0, 0xb, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0xff, 0xff, 0xf0, 0x1, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x3f, 0xff, 0xf8, 0x0, 0x3f,
    0xff, 0xff, 0x80, 0x0, 0x1f, 0xff, 0xfc, 0x0,
    0xb, 0xff, 0xff, 0xf8, 0x0, 0xf, 0xff, 0xff,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xd0, 0x7, 0xff,
    0xff, 0x40, 0x0, 0x3f, 0xff, 0xff, 0xfe, 0x43,
    0xff, 0xff, 0x80, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0xb, 0xff,
    0xff, 0x6f, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xb,
    0xff, 0xff, 0x40, 0xbf, 0xff, 0xff, 0xff, 0xc0,
    0x7, 0xff, 0xff, 0x80, 0x1, 0xff, 0xff, 0xff,
    0xf8, 0x3, 0xff, 0xff, 0xc0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x2, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xfc, 0x3f, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x1f, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xcb,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xfd, 0xbf, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x5f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xc7, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0xff, 0xff,
    0xff, 0x40, 0x0, 0xb, 0xff, 0xff, 0xfc, 0x3f,
    0xff, 0xff, 0xfd, 0x0, 0x1f, 0xff, 0xff, 0xfd,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xff, 0xfe,
    0x40, 0x0, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x0, 0x0, 0x14, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xf,
    0xff, 0xff, 0xfe, 0x40, 0xbf, 0xff, 0xff, 0xe0,
    0x0, 0xff, 0xff, 0xff, 0x40, 0x0, 0xff, 0xff,
    0xff, 0x0, 0x1f, 0xff, 0xff, 0xd0, 0x0, 0x3,
    0xff, 0xff, 0xf8, 0x2, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xff, 0xff, 0xfd, 0x3, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0xb, 0xff, 0xff, 0xe0,
    0x3f, 0xff, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x7, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xf4, 0x7f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0x47, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0xf8, 0x3f, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xc3,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xfc, 0x3f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xc2, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xff, 0xf0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xc0, 0xff, 0xff,
    0xff, 0xe4, 0x7, 0xff, 0xff, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xc0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xff, 0xff, 0xfc,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xf8, 0x1f, 0xff,
    0xff, 0xc0, 0x0, 0x2, 0xff, 0xff, 0xf8, 0x1,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x6b, 0xa4,
    0x0, 0x2f, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x14,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x80, 0x0,
    0x3, 0xd0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xff, 0xc0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x3f, 0xff, 0xd4, 0x1b, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xbf, 0xfe, 0x90, 0x0,
    0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0xff, 0xff, 0x80,
    0x3, 0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xf4,
    0x1f, 0xff, 0xff, 0xfc, 0x3f, 0xff, 0xff, 0xfd,
    0x3f, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0xfe,
    0x3f, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0xfe,
    0x2f, 0xff, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xf8,
    0x7, 0xff, 0xff, 0xf0, 0x2, 0xff, 0xff, 0xd0,
    0x0, 0x7f, 0xfe, 0x0, 0x0, 0x1, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x90, 0x0,
    0x0, 0x7f, 0xff, 0x0, 0x2, 0xff, 0xff, 0xd0,
    0xb, 0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xfc,
    0x2f, 0xff, 0xff, 0xfd, 0x3f, 0xff, 0xff, 0xfe,
    0x3f, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0xfe,
    0x3f, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0xfd,
    0x1f, 0xff, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xf4,
    0x3, 0xff, 0xff, 0xe0, 0x0, 0xff, 0xff, 0x80,
    0x0, 0x1f, 0xf8, 0x0,

    /* U+0048 "H" */
    0x55, 0x55, 0x55, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x55, 0x55, 0x53, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xcf, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xf3, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x3f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xcf, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf3, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xf3, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xcf, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xf3, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xf3, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x3f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xcf, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf3, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xfc, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xf0,

    /* U+004C "L" */
    0x55, 0x55, 0x55, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xfe, 0xaa, 0xaa,
    0xaa, 0xaa, 0xa7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+0059 "Y" */
    0x55, 0x55, 0x55, 0x50, 0x0, 0x0, 0x0, 0x1,
    0x55, 0x55, 0x55, 0xf, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xc2, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xd0, 0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xf0, 0xf, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf4,
    0x1, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xfc, 0x0, 0x3f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfd, 0x0, 0x7,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x0, 0x0, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x80, 0x0, 0x1f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xc0,
    0x0, 0x3, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xc0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x7, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x2, 0xff, 0xff, 0xfd, 0x0,
    0x2, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xc0, 0x0, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xf0, 0x0, 0x7f,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xfe, 0x0, 0x3f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xc0, 0xf, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xf4,
    0xb, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfe, 0x3, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xc1, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xf4, 0xbf, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xfe, 0x3f, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0,

    /* U+0061 "a" */
    0x0, 0x0, 0x0, 0x1a, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xb, 0xff, 0xf9, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xf9, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xfc, 0x0, 0x2e, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x1,
    0x6b, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x1a,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x1b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc3, 0xff, 0xff, 0xff, 0xf9, 0x2, 0xff,
    0xff, 0xff, 0x2f, 0xff, 0xff, 0xfd, 0x0, 0xb,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x2f, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xd0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xff,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xfb, 0xff,
    0xff, 0xf8, 0x0, 0x3, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xf4, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0x7f, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xff,
    0xff, 0xfe, 0xf, 0xff, 0xff, 0xc0, 0x1f, 0xff,
    0xff, 0xff, 0xd0, 0x2f, 0xff, 0xff, 0x0, 0xb,
    0xff, 0xff, 0xf4, 0x0, 0x7f, 0xff, 0xfc, 0x0,
    0x1, 0xbf, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x0, 0x0, 0x6b, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xfe, 0x40, 0x7f, 0xc0, 0x0, 0x3f, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x70, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x8, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x7, 0xf0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xe4, 0x1b, 0xff, 0x40,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x1a, 0xff, 0xf9, 0x0, 0x0,

    /* U+0065 "e" */
    0x0, 0x0, 0x0, 0x1, 0xbf, 0xfa, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xab, 0xff, 0xff, 0xff, 0x0, 0x2f, 0xff, 0xff,
    0xf8, 0x0, 0x2f, 0xff, 0xff, 0xc0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0x1, 0xff, 0xff, 0xf8, 0xb,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x3, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xc1, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xf4, 0xbf, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xfd, 0x3f, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x90, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x6, 0xfc, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xea, 0xbf, 0xff,
    0x80, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b,
    0xff, 0xe9, 0x0, 0x0, 0x0,

    /* U+0068 "h" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x40,
    0x0, 0xaf, 0xfe, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xc0, 0x7, 0xff, 0xff, 0xfd, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xe0, 0x3,
    0xff, 0xff, 0xfc, 0x2f, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x83, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x3f,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x3, 0xff, 0xff, 0xff, 0x3f, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xdf, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf7, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x7f, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0xff, 0xff, 0xff,
    0x7f, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xdf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xdf, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf7,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x7f, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0xff, 0xff, 0xff, 0x40,

    /* U+006C "l" */
    0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xf4,
    0x0, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff,
    0xf4, 0x0, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff,
    0xff, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x40, 0xf,
    0xff, 0xff, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x40,
    0xf, 0xff, 0xff, 0xf4, 0x0, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xf4, 0x0, 0xff, 0xff,
    0xff, 0x40, 0xf, 0xff, 0xff, 0xf4, 0x0, 0xff,
    0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xf4, 0x0,
    0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xf4,
    0x0, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff,
    0xf4, 0x0, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff,
    0xff, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x40, 0xf,
    0xff, 0xff, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x40,
    0xf, 0xff, 0xff, 0xf4, 0x0, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xf4, 0x0, 0xff, 0xff,
    0xff, 0x40, 0xf, 0xff, 0xff, 0xf4, 0x0, 0xff,
    0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xf4, 0x0,
    0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff, 0xf4,
    0x0, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff, 0xff,
    0xf4, 0x0, 0xff, 0xff, 0xff, 0x40, 0xf, 0xff,
    0xff, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x40, 0xf,
    0xff, 0xff, 0xf4, 0x0, 0xff, 0xff, 0xff, 0x40,
    0xf, 0xff, 0xff, 0xf4, 0x0, 0xff, 0xff, 0xff,
    0x40, 0xf, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0x80, 0xf, 0xff, 0xff, 0xfc, 0x0, 0xbf,
    0xff, 0xff, 0xe0, 0xb, 0xff, 0xff, 0xff, 0xf0,
    0x7f, 0xff, 0xff, 0xff, 0x43, 0xff, 0xff, 0xff,
    0xf8, 0x3f, 0xff, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xc0, 0x3f,
    0xff, 0xff, 0xfc, 0x1, 0xff, 0xff, 0xff, 0xd0,
    0x2, 0xff, 0xff, 0xfd, 0x0, 0x1, 0xbf, 0xfe,
    0x40,

    /* U+006F "o" */
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xfe, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xff, 0xff, 0xff,
    0xf4, 0x1b, 0xff, 0xff, 0xff, 0xd0, 0x7, 0xff,
    0xff, 0xff, 0x80, 0x0, 0xff, 0xff, 0xff, 0xf0,
    0xb, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xf4, 0xf, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xf8, 0x1f, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xfc, 0x2f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xfc,
    0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xfd, 0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xfd, 0x3f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfe, 0x3f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfe,
    0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xfe, 0x3f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xfe,
    0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xfd, 0x3f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfd, 0x2f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xfc, 0x1f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xf, 0xff, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xf8, 0xb, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xf4, 0x7, 0xff, 0xff, 0xff,
    0x80, 0x0, 0xff, 0xff, 0xff, 0xf0, 0x2, 0xff,
    0xff, 0xff, 0xf4, 0x1b, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xfe,
    0x80, 0x0, 0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0x55, 0x55, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0x0, 0x0, 0x1,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x4b, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x4b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x7, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xe0, 0x1,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfa,
    0x40,

    /* U+0077 "w" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xfe, 0x2f, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x40,
    0x0, 0x0, 0xff, 0xff, 0xfd, 0x1f, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0xff, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xc0, 0x0, 0x1,
    0xff, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xc0, 0x0, 0x2, 0xff,
    0xff, 0xfc, 0xb, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x3, 0xff, 0xff,
    0xf8, 0x7, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0x3, 0xff, 0xff, 0xf4,
    0x3, 0xff, 0xff, 0xfc, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x3, 0xff, 0xff, 0xf0, 0x3,
    0xff, 0xff, 0xfc, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x7, 0xff, 0xff, 0xf0, 0x2, 0xff,
    0xff, 0xfc, 0x0, 0x3, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xb, 0xff, 0xff, 0xe0, 0x1, 0xff, 0xff,
    0xfd, 0x0, 0x3, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xb, 0xff, 0xff, 0xd0, 0x0, 0xff, 0xff, 0xfe,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xf,
    0xff, 0xff, 0xc0, 0x0, 0xff, 0xff, 0xff, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xf, 0xff,
    0xff, 0xc0, 0x0, 0xbf, 0xff, 0xff, 0x0, 0xf,
    0xff, 0xfb, 0xff, 0xfd, 0x0, 0x1f, 0xff, 0xff,
    0x80, 0x0, 0x7f, 0xff, 0xff, 0x0, 0xf, 0xff,
    0xf7, 0xff, 0xfd, 0x0, 0x2f, 0xff, 0xff, 0x40,
    0x0, 0x3f, 0xff, 0xff, 0x40, 0x1f, 0xff, 0xe3,
    0xff, 0xfe, 0x0, 0x2f, 0xff, 0xff, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0x80, 0x2f, 0xff, 0xe3, 0xff,
    0xff, 0x0, 0x3f, 0xff, 0xff, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xc0, 0x2f, 0xff, 0xd3, 0xff, 0xff,
    0x0, 0x3f, 0xff, 0xfe, 0x0, 0x0, 0x2f, 0xff,
    0xff, 0xc0, 0x3f, 0xff, 0xc2, 0xff, 0xff, 0x40,
    0x7f, 0xff, 0xfd, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xc0, 0x3f, 0xff, 0xc1, 0xff, 0xff, 0x80, 0x7f,
    0xff, 0xfd, 0x0, 0x0, 0xf, 0xff, 0xff, 0xc0,
    0x7f, 0xff, 0xc1, 0xff, 0xff, 0xc0, 0xbf, 0xff,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0xff, 0xd0, 0xbf,
    0xff, 0x80, 0xff, 0xff, 0xc0, 0xbf, 0xff, 0xfc,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xd0, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xc0, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xe0, 0xff, 0xff, 0x40,
    0xbf, 0xff, 0xd0, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xf0, 0xff, 0xff, 0x0, 0xbf,
    0xff, 0xe0, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xf1, 0xff, 0xff, 0x0, 0x7f, 0xff,
    0xf1, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0x0, 0x3f, 0xff, 0xf1,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf2, 0xff, 0xfe, 0x0, 0x3f, 0xff, 0xf6, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xf7,
    0xff, 0xfd, 0x0, 0x2f, 0xff, 0xf6, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfb, 0xff,
    0xfc, 0x0, 0x1f, 0xff, 0xfb, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xf4, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 429, .box_w = 21, .box_h = 9, .ofs_x = 3, .ofs_y = 16},
    {.bitmap_index = 48, .adv_w = 682, .box_w = 38, .box_h = 54, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 561, .adv_w = 682, .box_w = 34, .box_h = 53, .ofs_x = 5, .ofs_y = 0},
    {.bitmap_index = 1012, .adv_w = 682, .box_w = 38, .box_h = 53, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1516, .adv_w = 682, .box_w = 38, .box_h = 54, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2029, .adv_w = 682, .box_w = 40, .box_h = 53, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2559, .adv_w = 682, .box_w = 38, .box_h = 54, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3072, .adv_w = 682, .box_w = 37, .box_h = 54, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 3572, .adv_w = 682, .box_w = 37, .box_h = 53, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 4063, .adv_w = 682, .box_w = 37, .box_h = 54, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 4563, .adv_w = 682, .box_w = 38, .box_h = 55, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5086, .adv_w = 393, .box_w = 16, .box_h = 41, .ofs_x = 4, .ofs_y = -1},
    {.bitmap_index = 5250, .adv_w = 867, .box_w = 43, .box_h = 53, .ofs_x = 6, .ofs_y = 0},
    {.bitmap_index = 5820, .adv_w = 670, .box_w = 33, .box_h = 53, .ofs_x = 6, .ofs_y = 0},
    {.bitmap_index = 6258, .adv_w = 681, .box_w = 45, .box_h = 53, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6855, .adv_w = 679, .box_w = 35, .box_h = 42, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 7223, .adv_w = 601, .box_w = 34, .box_h = 42, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 7580, .adv_w = 668, .box_w = 37, .box_h = 42, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 7969, .adv_w = 737, .box_w = 37, .box_h = 57, .ofs_x = 5, .ofs_y = 0},
    {.bitmap_index = 8497, .adv_w = 372, .box_w = 18, .box_h = 57, .ofs_x = 5, .ofs_y = -1},
    {.bitmap_index = 8754, .adv_w = 713, .box_w = 40, .box_h = 42, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 9174, .adv_w = 498, .box_w = 29, .box_h = 52, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9551, .adv_w = 1005, .box_w = 60, .box_h = 41, .ofs_x = 1, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9,
    0xa, 0xb, 0xc, 0xd, 0x1b, 0x1f, 0x2c, 0x34,
    0x36, 0x38, 0x3b, 0x3f, 0x42, 0x47, 0x4a
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 45, .range_length = 75, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 23, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, 3, 4,
    5, 6, 7, 8, 0, 9, 10, 11
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 3,
    4, 5, 5, 0, 0, 5, 6, 7
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, -100, 0, 0, -27, 0, 0,
    0, -48, 0, 0, 0, 0, -55, 0,
    -112, 0, -18, -37, -49, -100, -48, 0,
    -100, -73, -69, -37, 0, 0, -37, 0,
    0, -25, 0, -47, 0, -37, -13, -37,
    -13, 0, 13, 0, -48, -15, 0, -13,
    -10, 0, 0, -36, 0, 0, 0, 0,
    0, 0, -74, -15, 0, -36, -12, -48,
    3, -12, -26, -25, -26, 0, 0, 0,
    -37, -13, -12, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 11,
    .right_class_cnt     = 7,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 2,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t ui_font_pjfont70 = {
#else
lv_font_t ui_font_pjfont70 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 58,          /*The maximum line height required by the font*/
    .base_line = 1,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -9,
    .underline_thickness = 4,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if UI_FONT_PJFONT70*/

