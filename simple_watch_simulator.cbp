<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="Simple Watch Simulator" />
		<Option pch_mode="2" />
		<Option compiler="gcc" />
		<Build>
			<Target title="Debug">
				<Option output="bin/Debug/simple_watch_simulator" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Debug/" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-g" />
					<Add option="-std=c99" />
					<Add option="-Wall" />
					<Add option="-DWIN32" />
				</Compiler>
				<Linker>
					<Add library="gdi32" />
					<Add library="user32" />
					<Add library="kernel32" />
				</Linker>
			</Target>
			<Target title="Release">
				<Option output="bin/Release/simple_watch_simulator" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Release/" />
				<Option type="0" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-O2" />
					<Add option="-std=c99" />
					<Add option="-DWIN32" />
				</Compiler>
				<Linker>
					<Add option="-s" />
					<Add library="gdi32" />
					<Add library="user32" />
					<Add library="kernel32" />
				</Linker>
			</Target>
		</Build>
		<Unit filename="simple_main.c">
			<Option compilerVar="CC" />
		</Unit>
		<Extensions>
			<lib_finder disable_auto="1" />
		</Extensions>
	</Project>
</CodeBlocks_project_file>
