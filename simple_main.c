/**
 * @file simple_main.c
 * @brief 简化的LVGL手表模拟器主程序
 * 适用于Code::Blocks环境，不依赖复杂的驱动程序
 */

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <windows.h>

// 简化的LVGL模拟
typedef struct {
    int x, y;
} lv_point_t;

typedef struct {
    int x1, y1, x2, y2;
} lv_area_t;

typedef struct {
    unsigned char r, g, b;
} lv_color_t;

typedef enum {
    SCREEN_START = 0,
    SCREEN_HOME,
    SCREEN_SETTINGS
} screen_type_t;

// 模拟数据
typedef struct {
    float dose_rate;
    int battery_level;
    int heart_rate;
    int sao2;
    int csq;
    int gps_satellites;
} sensor_data_t;

typedef struct {
    int bt_enabled;
    int fly_mode;
    int gps_enabled;
    int auto_send;
} settings_data_t;

// 全局变量
static screen_type_t current_screen = SCREEN_START;
static sensor_data_t g_sensor = {0.25f, 75, 72, 98, 25, 8};
static settings_data_t g_settings = {0, 0, 1, 1};
static HWND hwnd;
static HDC hdc;

// 函数声明
void init_window(void);
void draw_start_screen(void);
void draw_home_screen(void);
void draw_settings_screen(void);
void handle_mouse_click(int x, int y);
void update_sensor_data(void);
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);

// 窗口过程
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg) {
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
            
        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            
            // 清除背景
            RECT rect;
            GetClientRect(hwnd, &rect);
            FillRect(hdc, &rect, (HBRUSH)GetStockObject(BLACK_BRUSH));
            
            // 根据当前屏幕绘制内容
            switch(current_screen) {
                case SCREEN_START:
                    draw_start_screen();
                    break;
                case SCREEN_HOME:
                    draw_home_screen();
                    break;
                case SCREEN_SETTINGS:
                    draw_settings_screen();
                    break;
            }
            
            EndPaint(hwnd, &ps);
            return 0;
        }
        
        case WM_LBUTTONDOWN: {
            int x = LOWORD(lParam);
            int y = HIWORD(lParam);
            handle_mouse_click(x, y);
            InvalidateRect(hwnd, NULL, TRUE);
            return 0;
        }
    }
    
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

// 初始化窗口
void init_window(void)
{
    WNDCLASS wc = {0};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = "LVGLWatchSimulator";
    wc.hbrBackground = (HBRUSH)GetStockObject(BLACK_BRUSH);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    
    RegisterClass(&wc);
    
    hwnd = CreateWindow(
        "LVGLWatchSimulator",
        "LVGL Watch Simulator",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        320, 320,
        NULL, NULL,
        GetModuleHandle(NULL),
        NULL
    );
    
    ShowWindow(hwnd, SW_SHOW);
    UpdateWindow(hwnd);
    hdc = GetDC(hwnd);
}

// 绘制启动屏幕
void draw_start_screen(void)
{
    SetTextColor(hdc, RGB(33, 150, 243)); // 蓝色
    SetBkColor(hdc, RGB(0, 0, 0)); // 黑色背景
    
    RECT rect = {50, 100, 250, 130};
    DrawText(hdc, "LVGL Watch", -1, &rect, DT_CENTER);
    
    SetTextColor(hdc, RGB(255, 255, 255)); // 白色
    rect.top = 140;
    rect.bottom = 160;
    DrawText(hdc, "v1.0.0", -1, &rect, DT_CENTER);
    
    SetTextColor(hdc, RGB(255, 152, 0)); // 橙色
    rect.top = 180;
    rect.bottom = 200;
    DrawText(hdc, "Click to continue", -1, &rect, DT_CENTER);
}

// 绘制主屏幕
void draw_home_screen(void)
{
    char buffer[100];
    RECT rect;
    
    // 时间显示
    SetTextColor(hdc, RGB(255, 255, 255));
    SetBkColor(hdc, RGB(0, 0, 0));
    
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    
    sprintf(buffer, "%02d:%02d", tm_info->tm_hour, tm_info->tm_min);
    rect = (RECT){80, 30, 220, 60};
    DrawText(hdc, buffer, -1, &rect, DT_CENTER);
    
    sprintf(buffer, "%04d-%02d-%02d", 
            tm_info->tm_year + 1900, 
            tm_info->tm_mon + 1, 
            tm_info->tm_mday);
    SetTextColor(hdc, RGB(255, 152, 0));
    rect = (RECT){80, 65, 220, 85};
    DrawText(hdc, buffer, -1, &rect, DT_CENTER);
    
    // 电池信息
    SetTextColor(hdc, RGB(76, 175, 80)); // 绿色
    sprintf(buffer, "Battery: %d%%", g_sensor.battery_level);
    rect = (RECT){20, 110, 150, 130};
    DrawText(hdc, buffer, -1, &rect, DT_LEFT);
    
    // 心率信息
    SetTextColor(hdc, RGB(255, 87, 34)); // 红色
    sprintf(buffer, "Heart: %d", g_sensor.heart_rate);
    rect = (RECT){170, 110, 280, 130};
    DrawText(hdc, buffer, -1, &rect, DT_LEFT);
    
    // 辐射剂量
    SetTextColor(hdc, RGB(255, 152, 0));
    rect = (RECT){50, 160, 250, 180};
    DrawText(hdc, "Dose Rate", -1, &rect, DT_CENTER);
    
    SetTextColor(hdc, RGB(255, 255, 255));
    sprintf(buffer, "%.2f μSv/h", g_sensor.dose_rate);
    rect = (RECT){50, 185, 250, 210};
    DrawText(hdc, buffer, -1, &rect, DT_CENTER);
    
    // 提示信息
    SetTextColor(hdc, RGB(128, 128, 128));
    rect = (RECT){50, 240, 250, 260};
    DrawText(hdc, "Long press for settings", -1, &rect, DT_CENTER);
}

// 绘制设置屏幕
void draw_settings_screen(void)
{
    char buffer[100];
    RECT rect;
    
    // 标题
    SetTextColor(hdc, RGB(33, 150, 243));
    SetBkColor(hdc, RGB(0, 0, 0));
    rect = (RECT){50, 20, 250, 50};
    DrawText(hdc, "Settings", -1, &rect, DT_CENTER);
    
    // 设置项
    SetTextColor(hdc, RGB(255, 255, 255));
    
    sprintf(buffer, "Bluetooth: %s", g_settings.bt_enabled ? "ON" : "OFF");
    rect = (RECT){30, 70, 270, 90};
    DrawText(hdc, buffer, -1, &rect, DT_LEFT);
    
    sprintf(buffer, "Flight Mode: %s", g_settings.fly_mode ? "ON" : "OFF");
    rect = (RECT){30, 100, 270, 120};
    DrawText(hdc, buffer, -1, &rect, DT_LEFT);
    
    sprintf(buffer, "GPS: %s", g_settings.gps_enabled ? "ON" : "OFF");
    rect = (RECT){30, 130, 270, 150};
    DrawText(hdc, buffer, -1, &rect, DT_LEFT);
    
    sprintf(buffer, "Auto Send: %s", g_settings.auto_send ? "ON" : "OFF");
    rect = (RECT){30, 160, 270, 180};
    DrawText(hdc, buffer, -1, &rect, DT_LEFT);
    
    // 状态信息
    SetTextColor(hdc, RGB(255, 152, 0));
    sprintf(buffer, "CSQ: %d  GPS Satellites: %d", g_sensor.csq, g_sensor.gps_satellites);
    rect = (RECT){30, 200, 270, 220};
    DrawText(hdc, buffer, -1, &rect, DT_LEFT);
    
    // 返回按钮
    SetTextColor(hdc, RGB(255, 255, 255));
    rect = (RECT){120, 240, 180, 260};
    DrawText(hdc, "Back", -1, &rect, DT_CENTER);
    Rectangle(hdc, 115, 235, 185, 265);
}

// 处理鼠标点击
void handle_mouse_click(int x, int y)
{
    static DWORD last_click_time = 0;
    DWORD current_time = GetTickCount();
    
    switch(current_screen) {
        case SCREEN_START:
            current_screen = SCREEN_HOME;
            break;
            
        case SCREEN_HOME:
            // 检查是否为长按（模拟）
            if(current_time - last_click_time < 500) {
                current_screen = SCREEN_SETTINGS;
            }
            break;
            
        case SCREEN_SETTINGS:
            // 点击设置项切换状态
            if(y >= 70 && y <= 90) {
                g_settings.bt_enabled = !g_settings.bt_enabled;
            } else if(y >= 100 && y <= 120) {
                g_settings.fly_mode = !g_settings.fly_mode;
            } else if(y >= 130 && y <= 150) {
                g_settings.gps_enabled = !g_settings.gps_enabled;
            } else if(y >= 160 && y <= 180) {
                g_settings.auto_send = !g_settings.auto_send;
            } else if(y >= 235 && y <= 265 && x >= 115 && x <= 185) {
                // 返回按钮
                current_screen = SCREEN_HOME;
            }
            break;
    }
    
    last_click_time = current_time;
}

// 更新传感器数据
void update_sensor_data(void)
{
    static DWORD last_update = 0;
    DWORD current_time = GetTickCount();
    
    if(current_time - last_update > 5000) { // 每5秒更新
        last_update = current_time;
        
        // 模拟数据变化
        g_sensor.dose_rate += (rand() % 100 - 50) * 0.01f;
        if(g_sensor.dose_rate < 0) g_sensor.dose_rate = 0;
        
        g_sensor.heart_rate = 60 + rand() % 40;
        g_sensor.battery_level = 20 + (current_time / 1000) % 80;
        g_sensor.csq = 15 + rand() % 20;
        g_sensor.gps_satellites = 4 + rand() % 8;
        
        InvalidateRect(hwnd, NULL, TRUE);
    }
}

// 主函数
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    srand(time(NULL));
    
    init_window();
    
    MSG msg;
    while(GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
        
        update_sensor_data();
        Sleep(10);
    }
    
    return 0;
}
