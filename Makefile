# LVGL手表模拟器 Makefile

CC = gcc
CFLAGS = -Wall -Wshadow -Wundef -Wmissing-prototypes -Wno-discarded-qualifiers -Wall -Wextra -Wno-unused-function -Wno-error=strict-prototypes -Wpointer-arith -fno-strict-aliasing -Wno-error=cpp -Wuninitialized -Wmaybe-uninitialized -Wno-unused-parameter -Wno-missing-field-initializers -Wtype-limits -Wsizeof-pointer-memaccess -Wno-format-nonliteral -Wno-cast-qual -Wunreachable-code -Wno-switch-default -Wreturn-type -Wmultichar -Wformat-security -Wno-ignored-qualifiers -Wno-error=pedantic -Wno-sign-compare -Wno-error=missing-prototypes -Wdouble-promotion -Wclobbered -Wdeprecated -Wempty-body -Wshift-negative-value -Wstack-usage=2048 -Wtype-limits -Wno-unused-parameter -Wno-missing-field-initializers

CFLAGS += -O3 -g0 -I./

# SDL2配置
LDFLAGS ?= -lSDL2 -lm
BIN = lvgl_simulator

# 源文件目录
LVGL_DIR_NAME ?= lvgl
LVGL_DIR ?= ${shell pwd}

# 包含LVGL源文件
include $(LVGL_DIR)/$(LVGL_DIR_NAME)/lvgl.mk
include $(LVGL_DIR)/$(LVGL_DIR_NAME)/src/lv_drivers/lv_drivers.mk

# UI应用源文件
CSRCS += lvgl_user/ui_app/ui.c
CSRCS += lvgl_user/ui_app/ui_helpers.c
CSRCS += lvgl_user/ui_app/screens/ui_HomeScreen.c
CSRCS += lvgl_user/ui_app/screens/ui_StartScreen.c
CSRCS += lvgl_user/ui_app/screens/ui_SysSetingScreen.c
CSRCS += lvgl_user/ui_app/components/ui_comp_hook.c

# 字体文件
CSRCS += lvgl_user/ui_app/fonts/ui_font_icon40.c
CSRCS += lvgl_user/ui_app/fonts/ui_font_pjfont25.c
CSRCS += lvgl_user/ui_app/fonts/ui_font_pjfont40.c
CSRCS += lvgl_user/ui_app/fonts/ui_font_pjfont70.c

# 主模拟器文件
CSRCS += simulator_main.c

OBJEXT ?= .o

AOBJS = $(ASRCS:.S=$(OBJEXT))
COBJS = $(CSRCS:.c=$(OBJEXT))

MAINOBJ = $(AOBJS) $(COBJS)

$(OBJEXT): %.c
	@$(CC)  $(CFLAGS) -c $< -o $@
	@echo "CC $<"
    
all: default

%.o: %.c
	@$(CC)  $(CFLAGS) -c $< -o $@
	@echo "CC $<"

default: $(AOBJS) $(COBJS)
	$(CC) -o $(BIN) $(MAINOBJ) $(LDFLAGS)

clean: 
	rm -f $(MAINOBJ) $(BIN)

install:
	@echo "安装SDL2开发库:"
	@echo "Ubuntu/Debian: sudo apt-get install libsdl2-dev"
	@echo "CentOS/RHEL: sudo yum install SDL2-devel"
	@echo "macOS: brew install sdl2"
	@echo "Windows: 下载SDL2开发库并配置"

.PHONY: clean install 