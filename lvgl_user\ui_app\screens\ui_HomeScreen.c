﻿// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.3.1
// LVGL version: 8.3.6
// Project name: LYwatch

#include "../ui.h"
#include "../../diver/timerDelay/tmdelay.h"
#include "../../diver/battery/battery.h"
#include "../../diver/csi_sensor/csi_sensor.h"

void ui_HomeScreen_screen_init(void)
{
    beijing_time_t t;
    get_beijing_time(&t, sys_unix_time);

    ui_HomeScreen = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_HomeScreen, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_TimePanel = lv_obj_create(ui_HomeScreen);
    lv_obj_set_width(ui_TimePanel, 320);
    lv_obj_set_height(ui_TimePanel, 121);
    lv_obj_set_x(ui_TimePanel, 0);
    lv_obj_set_y(ui_TimePanel, -144);
    lv_obj_set_align(ui_TimePanel, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_TimePanel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_TimePanel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_TimePanel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_TimePanel, lv_color_hex(0x535351), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_TimePanel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_HourLable = lv_label_create(ui_TimePanel);
    lv_obj_set_width(ui_HourLable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_HourLable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_HourLable, -90);
    lv_obj_set_y(ui_HourLable, 0);
    lv_obj_set_align(ui_HourLable, LV_ALIGN_TOP_MID);
    lv_label_set_text_fmt(ui_HourLable, "%02d", t.hour);
    lv_obj_set_style_text_letter_space(ui_HourLable, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui_HourLable, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_HourLable, &ui_font_pjfont70, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_MinuLable = lv_label_create(ui_TimePanel);
    lv_obj_set_width(ui_MinuLable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_MinuLable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_MinuLable, 90);
    lv_obj_set_y(ui_MinuLable, 0);
    lv_obj_set_align(ui_MinuLable, LV_ALIGN_TOP_MID);
    lv_label_set_text_fmt(ui_MinuLable, "%02d", t.min); 
    lv_obj_set_style_text_letter_space(ui_MinuLable, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui_MinuLable, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_MinuLable, &ui_font_pjfont70, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_DateLable = lv_label_create(ui_TimePanel);
    lv_obj_set_width(ui_DateLable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_DateLable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_DateLable, 0);
    lv_obj_set_y(ui_DateLable, 70);
    lv_obj_set_align(ui_DateLable, LV_ALIGN_TOP_MID);
    lv_label_set_text_fmt(ui_DateLable, "%04d-%02d-%02d", t.year, t.month, t.day);
    lv_obj_set_style_text_color(ui_DateLable, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_DateLable, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_DateLable, &ui_font_pjfont25, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Dot1Panle = lv_obj_create(ui_TimePanel);
    lv_obj_set_width(ui_Dot1Panle, 15);
    lv_obj_set_height(ui_Dot1Panle, 15);
    lv_obj_set_align(ui_Dot1Panle, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_Dot1Panle, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_Dot1Panle, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Dot1Panle, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Dot2Panle = lv_obj_create(ui_TimePanel);
    lv_obj_set_width(ui_Dot2Panle, 15);
    lv_obj_set_height(ui_Dot2Panle, 15);
    lv_obj_set_x(ui_Dot2Panle, 0);
    lv_obj_set_y(ui_Dot2Panle, -25);
    lv_obj_set_align(ui_Dot2Panle, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_Dot2Panle, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_Dot2Panle, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Dot2Panle, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_MainDataPanel = lv_obj_create(ui_HomeScreen);
    lv_obj_set_width(ui_MainDataPanel, 320);
    lv_obj_set_height(ui_MainDataPanel, 186);
    lv_obj_set_x(ui_MainDataPanel, -1);
    lv_obj_set_y(ui_MainDataPanel, 111);
    lv_obj_set_align(ui_MainDataPanel, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_MainDataPanel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_MainDataPanel, lv_color_hex(0x1E1E1D), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_MainDataPanel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_MainDataPanel, lv_color_hex(0x525250), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_MainDataPanel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_HeartArc = lv_arc_create(ui_MainDataPanel);
    lv_obj_set_width(ui_HeartArc, 70);
    lv_obj_set_height(ui_HeartArc, 70);
    lv_obj_set_x(ui_HeartArc, 150);
    lv_obj_set_y(ui_HeartArc, 0);
    lv_obj_set_align(ui_HeartArc, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_HeartArc, LV_OBJ_FLAG_CLICKABLE);      /// Flags
    lv_arc_set_value(ui_HeartArc, 70);
    lv_arc_set_bg_angles(ui_HeartArc, 90, 270);
    lv_obj_set_style_arc_color(ui_HeartArc, lv_color_hex(0x7E3E25), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui_HeartArc, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_width(ui_HeartArc, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_rounded(ui_HeartArc, false, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_arc_color(ui_HeartArc, lv_color_hex(0xFF4900), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui_HeartArc, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_width(ui_HeartArc, 15, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_rounded(ui_HeartArc, false, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_HeartArc, lv_color_hex(0xFFB600), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_HeartArc, 0, LV_PART_KNOB | LV_STATE_DEFAULT);

    ui_SaO2Arc = lv_arc_create(ui_MainDataPanel);
    lv_obj_set_width(ui_SaO2Arc, 120);
    lv_obj_set_height(ui_SaO2Arc, 120);
    lv_obj_set_x(ui_SaO2Arc, 150);
    lv_obj_set_y(ui_SaO2Arc, 0);
    lv_obj_set_align(ui_SaO2Arc, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_SaO2Arc, LV_OBJ_FLAG_CLICKABLE);      /// Flags
    lv_arc_set_value(ui_SaO2Arc, 70);
    lv_arc_set_bg_angles(ui_SaO2Arc, 90, 270);
    lv_obj_set_style_arc_color(ui_SaO2Arc, lv_color_hex(0x934E4E), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui_SaO2Arc, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_width(ui_SaO2Arc, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_rounded(ui_SaO2Arc, false, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_arc_color(ui_SaO2Arc, lv_color_hex(0xFF0000), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui_SaO2Arc, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_width(ui_SaO2Arc, 15, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_rounded(ui_SaO2Arc, false, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_SaO2Arc, lv_color_hex(0xFFB600), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_SaO2Arc, 0, LV_PART_KNOB | LV_STATE_DEFAULT);

    ui_BatArc = lv_arc_create(ui_MainDataPanel);
    lv_obj_set_width(ui_BatArc, 170);
    lv_obj_set_height(ui_BatArc, 170);
    lv_obj_set_x(ui_BatArc, 150);
    lv_obj_set_y(ui_BatArc, 0);
    lv_obj_set_align(ui_BatArc, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_BatArc, LV_OBJ_FLAG_CLICKABLE);      /// Flags
    lv_arc_set_value(ui_BatArc, 70);
    lv_arc_set_bg_angles(ui_BatArc, 90, 270);
    lv_obj_set_style_arc_color(ui_BatArc, lv_color_hex(0x255A18), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui_BatArc, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_width(ui_BatArc, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_rounded(ui_BatArc, false, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_arc_color(ui_BatArc, lv_color_hex(0x24B600), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_opa(ui_BatArc, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_width(ui_BatArc, 15, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_arc_rounded(ui_BatArc, false, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_BatArc, lv_color_hex(0xFF6D00), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BatArc, 0, LV_PART_KNOB | LV_STATE_DEFAULT);

    ui_Label7 = lv_label_create(ui_MainDataPanel);
    lv_obj_set_width(ui_Label7, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label7, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label7, 0);
    lv_obj_set_y(ui_Label7, 55);
    lv_label_set_text(ui_Label7, "");
    lv_obj_set_style_text_color(ui_Label7, lv_color_hex(0xFF0000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label7, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label7, &ui_font_icon40, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SaO2Lable = lv_label_create(ui_MainDataPanel);
    lv_obj_set_width(ui_SaO2Lable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_SaO2Lable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_SaO2Lable, 50);
    lv_obj_set_y(ui_SaO2Lable, 70);
    lv_label_set_text(ui_SaO2Lable, "80%");
    lv_obj_set_style_text_color(ui_SaO2Lable, lv_color_hex(0xFF0000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_SaO2Lable, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_SaO2Lable, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_SaO2Lable, &ui_font_pjfont25, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_HeartLable = lv_label_create(ui_MainDataPanel);
    lv_obj_set_width(ui_HeartLable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_HeartLable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_HeartLable, 50);
    lv_obj_set_y(ui_HeartLable, 20);
    lv_label_set_text(ui_HeartLable, "75");
    lv_obj_set_style_text_color(ui_HeartLable, lv_color_hex(0xFF4900), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_HeartLable, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_HeartLable, LV_TEXT_ALIGN_LEFT, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_HeartLable, &ui_font_pjfont25, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label2 = lv_label_create(ui_MainDataPanel);
    lv_obj_set_width(ui_Label2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label2, -7);
    lv_obj_set_y(ui_Label2, 110);
    lv_label_set_text(ui_Label2, "");
    lv_obj_set_style_text_color(ui_Label2, lv_color_hex(0x31A231), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label2, &ui_font_icon40, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label1 = lv_label_create(ui_MainDataPanel);
    lv_obj_set_width(ui_Label1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label1, 0);
    lv_obj_set_y(ui_Label1, 5);
    lv_label_set_text(ui_Label1, "");
    lv_obj_set_style_text_color(ui_Label1, lv_color_hex(0xFF4900), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label1, &ui_font_icon40, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BatLable = lv_label_create(ui_MainDataPanel);
    lv_obj_set_width(ui_BatLable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_BatLable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_BatLable, 50);
    lv_obj_set_y(ui_BatLable, 120);

    static uint8_t if_first_start = 1;
    lv_color_t bt_color = lv_color_hex(0x24B600);
    if(!if_first_start){
        bt_color = dev_battery_prt > 10 ? bt_color : lv_color_hex(0xFF0000);
        lv_label_set_text_fmt(ui_BatLable, "%d%%", dev_battery_prt);
    }
    if_first_start = 0;
    
    lv_obj_set_style_text_color(ui_BatLable, bt_color, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_BatLable, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_BatLable, &ui_font_pjfont25, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_DoseRatePanel = lv_obj_create(ui_HomeScreen);
    lv_obj_set_width(ui_DoseRatePanel, 320);
    lv_obj_set_height(ui_DoseRatePanel, 71);
    lv_obj_set_x(ui_DoseRatePanel, 0);
    lv_obj_set_y(ui_DoseRatePanel, -33);
    lv_obj_set_align(ui_DoseRatePanel, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_DoseRatePanel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_Label3 = lv_label_create(ui_DoseRatePanel);
    lv_obj_set_width(ui_Label3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Label3, LV_ALIGN_LEFT_MID);
    lv_label_set_text(ui_Label3, "");
    lv_obj_set_style_text_color(ui_Label3, lv_color_hex(0xFFB600), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label3, &ui_font_icon40, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_DoseRateLable = lv_label_create(ui_DoseRatePanel);
    lv_obj_set_width(ui_DoseRateLable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_DoseRateLable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_DoseRateLable, LV_ALIGN_CENTER);
    char dose_rate_string[20];
    sprintf(dose_rate_string,"%0.2f", csi_sensor.dose_rate);
    lv_label_set_text(ui_DoseRateLable, dose_rate_string);
    lv_obj_set_style_text_color(ui_DoseRateLable, lv_color_hex(0xFFB600), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_DoseRateLable, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_DoseRateLable, &ui_font_pjXiFont40, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label4 = lv_label_create(ui_DoseRatePanel);
    lv_obj_set_width(ui_Label4, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label4, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Label4, LV_ALIGN_RIGHT_MID);
    lv_label_set_text(ui_Label4, "uSv/h");
    lv_obj_set_style_text_color(ui_Label4, lv_color_hex(0xFFB600), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_Label4, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label4, &ui_font_pjXiFont20, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(ui_HomeScreen, ui_event_HomeScreen, LV_EVENT_ALL, NULL);

}
