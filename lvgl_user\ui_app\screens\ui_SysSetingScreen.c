﻿// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.3.1
// LVGL version: 8.3.6
// Project name: LYwatch

#include "../ui.h"
#include "user_display.h"
#include "../system_set.h"
#include "../../../diver/ec800m/ec800m.h"

void ui_SysSetingScreen_screen_init(void)
{
    ui_SysSetingScreen = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_SysSetingScreen, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_SetingPanle = lv_obj_create(ui_SysSetingScreen);
    lv_obj_set_width(ui_SetingPanle, 320);
    lv_obj_set_height(ui_SetingPanle, 400);
    lv_obj_set_x(ui_SetingPanle, 0);
    lv_obj_set_y(ui_SetingPanle, -2);
    lv_obj_set_align(ui_SetingPanle, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_SetingPanle, LV_OBJ_FLAG_OVERFLOW_VISIBLE | LV_OBJ_FLAG_EVENT_BUBBLE);     /// Flags
    lv_obj_clear_flag(ui_SetingPanle, LV_OBJ_FLAG_PRESS_LOCK | LV_OBJ_FLAG_CLICK_FOCUSABLE | LV_OBJ_FLAG_GESTURE_BUBBLE |
                      LV_OBJ_FLAG_SNAPPABLE | LV_OBJ_FLAG_SCROLL_MOMENTUM | LV_OBJ_FLAG_SCROLL_CHAIN);     /// Flags
    lv_obj_set_style_bg_color(ui_SetingPanle, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_SetingPanle, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_SetingPanle, lv_color_hex(0x494955), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_SetingPanle, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label5 = lv_label_create(ui_SetingPanle);
    lv_obj_set_width(ui_Label5, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label5, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label5, 0);
    lv_obj_set_y(ui_Label5, -162);
    lv_obj_set_align(ui_Label5, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label5, "系统");
    lv_obj_set_style_text_font(ui_Label5, &ui_font_pjfont40, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_Label6 = lv_label_create(ui_SetingPanle);
    lv_obj_set_width(ui_Label6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Label6, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_Label6, -63);
    lv_obj_set_y(ui_Label6, 23);
    lv_obj_set_align(ui_Label6, LV_ALIGN_CENTER);
    lv_label_set_text(ui_Label6,
                      "蓝牙开关\n飞行模式\n卫星定位\n手动上报\n自动上报\n信号质量\n卫星个数");
    lv_label_set_recolor(ui_Label6, "true");
    lv_obj_set_style_text_letter_space(ui_Label6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui_Label6, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_Label6, &ui_font_pjXiFont30, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_BTSwitch = lv_switch_create(ui_SetingPanle);
    //add(必须直接放在按键创建之后、其他函数之前):
    if(system_setings.bt_switch){
        lv_obj_add_state(ui_BTSwitch, LV_STATE_CHECKED);
    }
    lv_obj_add_event_cb(ui_BTSwitch, lv_switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);

    lv_obj_set_width(ui_BTSwitch, 71);
    lv_obj_set_height(ui_BTSwitch, 25);
    lv_obj_set_x(ui_BTSwitch, 100);
    lv_obj_set_y(ui_BTSwitch, -106);
    lv_obj_set_align(ui_BTSwitch, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_BTSwitch, lv_color_hex(0x494955), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BTSwitch, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    

    ui_FlyModeSwitch = lv_switch_create(ui_SetingPanle);
    //add(必须直接放在按键创建之后、其他函数之前):
    if(system_setings.fly_mode_switch){
        lv_obj_add_state(ui_FlyModeSwitch, LV_STATE_CHECKED);
    }
    lv_obj_add_event_cb(ui_FlyModeSwitch, lv_switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);

    lv_obj_set_width(ui_FlyModeSwitch, 71);
    lv_obj_set_height(ui_FlyModeSwitch, 25);
    lv_obj_set_x(ui_FlyModeSwitch, 100);
    lv_obj_set_y(ui_FlyModeSwitch, -63);
    lv_obj_set_align(ui_FlyModeSwitch, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_FlyModeSwitch, lv_color_hex(0x494955), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_FlyModeSwitch, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    

    ui_GpsSwitch = lv_switch_create(ui_SetingPanle);
    //add(必须直接放在按键创建之后、其他函数之前):
    if(system_setings.gnss_switch){
        lv_obj_add_state(ui_GpsSwitch, LV_STATE_CHECKED);
    }
    lv_obj_add_event_cb(ui_GpsSwitch, lv_switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);

    lv_obj_set_width(ui_GpsSwitch, 71);
    lv_obj_set_height(ui_GpsSwitch, 25);
    lv_obj_set_x(ui_GpsSwitch, 100);
    lv_obj_set_y(ui_GpsSwitch, -20);
    lv_obj_set_align(ui_GpsSwitch, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_GpsSwitch, lv_color_hex(0x494955), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_GpsSwitch, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    

    ui_SendDataNowSwitch = lv_switch_create(ui_SetingPanle);
    //add(必须直接放在按键创建之后、其他函数之前):
    lv_obj_add_event_cb(ui_SendDataNowSwitch, lv_switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);

    lv_obj_set_width(ui_SendDataNowSwitch, 71);
    lv_obj_set_height(ui_SendDataNowSwitch, 25);
    lv_obj_set_x(ui_SendDataNowSwitch, 100);
    lv_obj_set_y(ui_SendDataNowSwitch, 23);
    lv_obj_set_align(ui_SendDataNowSwitch, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_SendDataNowSwitch, lv_color_hex(0x494955), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_SendDataNowSwitch, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    

    ui_AutoSendSwitch = lv_switch_create(ui_SetingPanle);
    //add(必须直接放在按键创建之后、其他函数之前):
    if(system_setings.auto_upsend_switch){
        lv_obj_add_state(ui_AutoSendSwitch, LV_STATE_CHECKED);
    }
    lv_obj_add_event_cb(ui_AutoSendSwitch, lv_switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    
    lv_obj_set_width(ui_AutoSendSwitch, 71);
    lv_obj_set_height(ui_AutoSendSwitch, 25);
    lv_obj_set_x(ui_AutoSendSwitch, 100);
    lv_obj_set_y(ui_AutoSendSwitch, 66);
    lv_obj_set_align(ui_AutoSendSwitch, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_AutoSendSwitch, lv_color_hex(0x494955), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_AutoSendSwitch, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    

    ui_CSQLabel = lv_label_create(ui_SetingPanle);
    lv_obj_set_width(ui_CSQLabel, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_CSQLabel, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_CSQLabel, 100);
    lv_obj_set_y(ui_CSQLabel, 110);
    lv_obj_set_align(ui_CSQLabel, LV_ALIGN_CENTER);
    lv_label_set_text_fmt(ui_CSQLabel, "%d", default_ec800m.csq);
    lv_obj_set_style_text_font(ui_CSQLabel, &ui_font_pjXiFont30, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_GPSNumLabel = lv_label_create(ui_SetingPanle);
    lv_obj_set_width(ui_GPSNumLabel, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_GPSNumLabel, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_GPSNumLabel, 100);
    lv_obj_set_y(ui_GPSNumLabel, 155);
    lv_obj_set_align(ui_GPSNumLabel, LV_ALIGN_CENTER);
    lv_label_set_text_fmt(ui_GPSNumLabel, "%d", default_ec800m.gnss_star_num);
    lv_obj_set_style_text_font(ui_GPSNumLabel, &ui_font_pjXiFont30, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_UpSendLable = lv_label_create(ui_SetingPanle);
    lv_obj_set_width(ui_UpSendLable, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_UpSendLable, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_UpSendLable, 120);
    lv_obj_set_y(ui_UpSendLable, -161);
    lv_obj_set_align(ui_UpSendLable, LV_ALIGN_CENTER);
    lv_label_set_text(ui_UpSendLable, "");
    lv_obj_set_style_text_font(ui_UpSendLable, &ui_font_icon40, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_add_flag(ui_UpSendLable, LV_OBJ_FLAG_HIDDEN);

    lv_obj_add_event_cb(ui_SysSetingScreen, ui_event_SysSetingScreen, LV_EVENT_ALL, NULL);
}
