#include "my_gui.h"
#include "lvgl/lvgl.h"
#include <stdio.h>
#include <time.h>
#include <stdlib.h>

// 屏幕枚举
typedef enum {
    SCREEN_START = 0,
    SCREEN_HOME,
    SCREEN_SETTINGS
} screen_type_t;

// 全局变量
static lv_obj_t* current_screen = NULL;
static screen_type_t current_screen_type = SCREEN_START;

// 模拟数据结构
typedef struct {
    int year, month, day, hour, min, sec;
} time_data_t;

typedef struct {
    float dose_rate;
    int battery_level;
    int heart_rate;
    int sao2;
    int csq;
    int gps_satellites;
} sensor_data_t;

typedef struct {
    bool bt_enabled;
    bool fly_mode;
    bool gps_enabled;
    bool auto_send;
    bool manual_send;
} settings_data_t;

// 全局数据
static sensor_data_t g_sensor_data = {0.25f, 75, 72, 98, 25, 8};
static settings_data_t g_settings = {false, false, true, true, false};
static time_data_t g_time_data = {2024, 8, 4, 14, 30, 0};

// 字体声明（使用LVGL内置字体）
#define FONT_LARGE &lv_font_montserrat_14
#define FONT_MEDIUM &lv_font_montserrat_14
#define FONT_SMALL &lv_font_montserrat_14

// 颜色定义
#define COLOR_PRIMARY lv_color_hex(0x2196F3)
#define COLOR_SECONDARY lv_color_hex(0xFF9800)
#define COLOR_SUCCESS lv_color_hex(0x4CAF50)
#define COLOR_WARNING lv_color_hex(0xFF5722)

// 前向声明
static void create_start_screen(void);
static void create_home_screen(void);
static void create_settings_screen(void);
static void switch_to_screen(screen_type_t screen_type);
static void update_time_display(void);
static void update_sensor_display(void);

// 事件处理函数
static void start_screen_event_cb(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    if(code == LV_EVENT_CLICKED) {
        switch_to_screen(SCREEN_HOME);
    }
}

static void home_screen_event_cb(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    if(code == LV_EVENT_LONG_PRESSED) {
        switch_to_screen(SCREEN_SETTINGS);
    }
}

static void settings_back_event_cb(lv_event_t* e)
{
    lv_event_code_t code = lv_event_get_code(e);
    if(code == LV_EVENT_CLICKED) {
        switch_to_screen(SCREEN_HOME);
    }
}

static void switch_event_cb(lv_event_t* e)
{
    lv_obj_t* switch_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    
    if(code == LV_EVENT_VALUE_CHANGED) {
        bool state = lv_obj_has_state(switch_obj, LV_STATE_CHECKED);
        
        // 根据开关的用户数据确定是哪个设置
        int* setting_type = (int*)lv_obj_get_user_data(switch_obj);
        if(setting_type) {
            switch(*setting_type) {
                case 0: g_settings.bt_enabled = state; break;
                case 1: g_settings.fly_mode = state; break;
                case 2: g_settings.gps_enabled = state; break;
                case 3: g_settings.auto_send = state; break;
                case 4: g_settings.manual_send = state; break;
            }
        }
    }
}

// 创建启动屏幕
static void create_start_screen(void)
{
    current_screen = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(current_screen, lv_color_black(), 0);
    
    // 标题标签
    lv_obj_t* title_label = lv_label_create(current_screen);
    lv_label_set_text(title_label, "LVGL Watch");
    lv_obj_set_style_text_font(title_label, FONT_LARGE, 0);
    lv_obj_set_style_text_color(title_label, COLOR_PRIMARY, 0);
    lv_obj_align(title_label, LV_ALIGN_CENTER, 0, -40);
    
    // 版本标签
    lv_obj_t* version_label = lv_label_create(current_screen);
    lv_label_set_text(version_label, "v1.0.0");
    lv_obj_set_style_text_font(version_label, FONT_SMALL, 0);
    lv_obj_set_style_text_color(version_label, lv_color_white(), 0);
    lv_obj_align(version_label, LV_ALIGN_CENTER, 0, 0);
    
    // 提示标签
    lv_obj_t* hint_label = lv_label_create(current_screen);
    lv_label_set_text(hint_label, "Click to continue");
    lv_obj_set_style_text_font(hint_label, FONT_SMALL, 0);
    lv_obj_set_style_text_color(hint_label, COLOR_SECONDARY, 0);
    lv_obj_align(hint_label, LV_ALIGN_CENTER, 0, 40);
    
    // 添加点击事件
    lv_obj_add_event_cb(current_screen, start_screen_event_cb, LV_EVENT_CLICKED, NULL);
    lv_obj_add_flag(current_screen, LV_OBJ_FLAG_CLICKABLE);
    
    lv_scr_load(current_screen);
}

// 创建主屏幕
static void create_home_screen(void)
{
    current_screen = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(current_screen, lv_color_black(), 0);
    
    // 时间面板
    lv_obj_t* time_panel = lv_obj_create(current_screen);
    lv_obj_set_size(time_panel, 200, 80);
    lv_obj_align(time_panel, LV_ALIGN_TOP_MID, 0, 10);
    lv_obj_set_style_bg_color(time_panel, lv_color_hex(0x1a1a1a), 0);
    lv_obj_set_style_border_width(time_panel, 0, 0);
    
    // 时间标签
    lv_obj_t* time_label = lv_label_create(time_panel);
    lv_label_set_text(time_label, "14:30");
    lv_obj_set_style_text_font(time_label, FONT_LARGE, 0);
    lv_obj_set_style_text_color(time_label, lv_color_white(), 0);
    lv_obj_align(time_label, LV_ALIGN_CENTER, 0, -10);
    
    // 日期标签
    lv_obj_t* date_label = lv_label_create(time_panel);
    lv_label_set_text(date_label, "2024-08-04");
    lv_obj_set_style_text_font(date_label, FONT_SMALL, 0);
    lv_obj_set_style_text_color(date_label, COLOR_SECONDARY, 0);
    lv_obj_align(date_label, LV_ALIGN_CENTER, 0, 15);
    
    // 数据面板
    lv_obj_t* data_panel = lv_obj_create(current_screen);
    lv_obj_set_size(data_panel, 200, 100);
    lv_obj_align(data_panel, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_bg_color(data_panel, lv_color_hex(0x1a1a1a), 0);
    lv_obj_set_style_border_width(data_panel, 0, 0);
    
    // 电池弧形
    lv_obj_t* bat_arc = lv_arc_create(data_panel);
    lv_obj_set_size(bat_arc, 60, 60);
    lv_obj_align(bat_arc, LV_ALIGN_LEFT_MID, 10, 0);
    lv_arc_set_value(bat_arc, g_sensor_data.battery_level);
    lv_obj_set_style_arc_color(bat_arc, COLOR_SUCCESS, LV_PART_INDICATOR);
    
    // 电池标签
    lv_obj_t* bat_label = lv_label_create(bat_arc);
    lv_label_set_text_fmt(bat_label, "%d%%", g_sensor_data.battery_level);
    lv_obj_set_style_text_font(bat_label, FONT_SMALL, 0);
    lv_obj_align(bat_label, LV_ALIGN_CENTER, 0, 0);
    
    // 心率弧形
    lv_obj_t* heart_arc = lv_arc_create(data_panel);
    lv_obj_set_size(heart_arc, 60, 60);
    lv_obj_align(heart_arc, LV_ALIGN_RIGHT_MID, -10, 0);
    lv_arc_set_value(heart_arc, g_sensor_data.heart_rate);
    lv_obj_set_style_arc_color(heart_arc, COLOR_WARNING, LV_PART_INDICATOR);
    
    // 心率标签
    lv_obj_t* heart_label = lv_label_create(heart_arc);
    lv_label_set_text_fmt(heart_label, "%d", g_sensor_data.heart_rate);
    lv_obj_set_style_text_font(heart_label, FONT_SMALL, 0);
    lv_obj_align(heart_label, LV_ALIGN_CENTER, 0, 0);
    
    // 辐射剂量面板
    lv_obj_t* dose_panel = lv_obj_create(current_screen);
    lv_obj_set_size(dose_panel, 200, 60);
    lv_obj_align(dose_panel, LV_ALIGN_BOTTOM_MID, 0, -10);
    lv_obj_set_style_bg_color(dose_panel, lv_color_hex(0x1a1a1a), 0);
    lv_obj_set_style_border_width(dose_panel, 0, 0);
    
    // 剂量率标签
    lv_obj_t* dose_title = lv_label_create(dose_panel);
    lv_label_set_text(dose_title, "Dose Rate");
    lv_obj_set_style_text_font(dose_title, FONT_SMALL, 0);
    lv_obj_set_style_text_color(dose_title, COLOR_SECONDARY, 0);
    lv_obj_align(dose_title, LV_ALIGN_TOP_MID, 0, 5);
    
    lv_obj_t* dose_value = lv_label_create(dose_panel);
    lv_label_set_text_fmt(dose_value, "%.2f μSv/h", g_sensor_data.dose_rate);
    lv_obj_set_style_text_font(dose_value, FONT_MEDIUM, 0);
    lv_obj_set_style_text_color(dose_value, lv_color_white(), 0);
    lv_obj_align(dose_value, LV_ALIGN_BOTTOM_MID, 0, -5);
    
    // 添加长按事件进入设置
    lv_obj_add_event_cb(current_screen, home_screen_event_cb, LV_EVENT_LONG_PRESSED, NULL);
    lv_obj_add_flag(current_screen, LV_OBJ_FLAG_CLICKABLE);
    
    lv_scr_load(current_screen);
}

// 创建设置屏幕
static void create_settings_screen(void)
{
    current_screen = lv_obj_create(NULL);
    lv_obj_set_style_bg_color(current_screen, lv_color_black(), 0);

    // 标题
    lv_obj_t* title = lv_label_create(current_screen);
    lv_label_set_text(title, "Settings");
    lv_obj_set_style_text_font(title, FONT_LARGE, 0);
    lv_obj_set_style_text_color(title, COLOR_PRIMARY, 0);
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 10);

    // 设置面板
    lv_obj_t* settings_panel = lv_obj_create(current_screen);
    lv_obj_set_size(settings_panel, 200, 160);
    lv_obj_align(settings_panel, LV_ALIGN_CENTER, 0, 10);
    lv_obj_set_style_bg_color(settings_panel, lv_color_hex(0x1a1a1a), 0);
    lv_obj_set_style_border_width(settings_panel, 0, 0);

    // 蓝牙开关
    static int bt_type = 0;
    lv_obj_t* bt_label = lv_label_create(settings_panel);
    lv_label_set_text(bt_label, "Bluetooth");
    lv_obj_set_style_text_font(bt_label, FONT_SMALL, 0);
    lv_obj_align(bt_label, LV_ALIGN_TOP_LEFT, 10, 10);

    lv_obj_t* bt_switch = lv_switch_create(settings_panel);
    lv_obj_align(bt_switch, LV_ALIGN_TOP_RIGHT, -10, 5);
    lv_obj_set_user_data(bt_switch, &bt_type);
    lv_obj_add_event_cb(bt_switch, switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    if(g_settings.bt_enabled) lv_obj_add_state(bt_switch, LV_STATE_CHECKED);

    // 飞行模式开关
    static int fly_type = 1;
    lv_obj_t* fly_label = lv_label_create(settings_panel);
    lv_label_set_text(fly_label, "Flight Mode");
    lv_obj_set_style_text_font(fly_label, FONT_SMALL, 0);
    lv_obj_align(fly_label, LV_ALIGN_TOP_LEFT, 10, 40);

    lv_obj_t* fly_switch = lv_switch_create(settings_panel);
    lv_obj_align(fly_switch, LV_ALIGN_TOP_RIGHT, -10, 35);
    lv_obj_set_user_data(fly_switch, &fly_type);
    lv_obj_add_event_cb(fly_switch, switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    if(g_settings.fly_mode) lv_obj_add_state(fly_switch, LV_STATE_CHECKED);

    // GPS开关
    static int gps_type = 2;
    lv_obj_t* gps_label = lv_label_create(settings_panel);
    lv_label_set_text(gps_label, "GPS");
    lv_obj_set_style_text_font(gps_label, FONT_SMALL, 0);
    lv_obj_align(gps_label, LV_ALIGN_TOP_LEFT, 10, 70);

    lv_obj_t* gps_switch = lv_switch_create(settings_panel);
    lv_obj_align(gps_switch, LV_ALIGN_TOP_RIGHT, -10, 65);
    lv_obj_set_user_data(gps_switch, &gps_type);
    lv_obj_add_event_cb(gps_switch, switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    if(g_settings.gps_enabled) lv_obj_add_state(gps_switch, LV_STATE_CHECKED);

    // 自动发送开关
    static int auto_type = 3;
    lv_obj_t* auto_label = lv_label_create(settings_panel);
    lv_label_set_text(auto_label, "Auto Send");
    lv_obj_set_style_text_font(auto_label, FONT_SMALL, 0);
    lv_obj_align(auto_label, LV_ALIGN_TOP_LEFT, 10, 100);

    lv_obj_t* auto_switch = lv_switch_create(settings_panel);
    lv_obj_align(auto_switch, LV_ALIGN_TOP_RIGHT, -10, 95);
    lv_obj_set_user_data(auto_switch, &auto_type);
    lv_obj_add_event_cb(auto_switch, switch_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    if(g_settings.auto_send) lv_obj_add_state(auto_switch, LV_STATE_CHECKED);

    // 状态信息
    lv_obj_t* status_label = lv_label_create(settings_panel);
    lv_label_set_text_fmt(status_label, "CSQ: %d  GPS: %d", g_sensor_data.csq, g_sensor_data.gps_satellites);
    lv_obj_set_style_text_font(status_label, FONT_SMALL, 0);
    lv_obj_set_style_text_color(status_label, COLOR_SECONDARY, 0);
    lv_obj_align(status_label, LV_ALIGN_BOTTOM_MID, 0, -10);

    // 返回按钮
    lv_obj_t* back_btn = lv_btn_create(current_screen);
    lv_obj_set_size(back_btn, 80, 30);
    lv_obj_align(back_btn, LV_ALIGN_BOTTOM_MID, 0, -10);
    lv_obj_add_event_cb(back_btn, settings_back_event_cb, LV_EVENT_CLICKED, NULL);

    lv_obj_t* back_label = lv_label_create(back_btn);
    lv_label_set_text(back_label, "Back");
    lv_obj_center(back_label);

    lv_scr_load(current_screen);
}

// 屏幕切换函数
static void switch_to_screen(screen_type_t screen_type)
{
    if(current_screen) {
        lv_obj_del(current_screen);
        current_screen = NULL;
    }

    current_screen_type = screen_type;

    switch(screen_type) {
        case SCREEN_START:
            create_start_screen();
            break;
        case SCREEN_HOME:
            create_home_screen();
            break;
        case SCREEN_SETTINGS:
            create_settings_screen();
            break;
    }
}

// 更新时间显示
static void update_time_display(void)
{
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);

    g_time_data.year = tm_info->tm_year + 1900;
    g_time_data.month = tm_info->tm_mon + 1;
    g_time_data.day = tm_info->tm_mday;
    g_time_data.hour = tm_info->tm_hour;
    g_time_data.min = tm_info->tm_min;
    g_time_data.sec = tm_info->tm_sec;
}

// 定时器回调函数
static void update_timer_cb(lv_timer_t* timer)
{
    (void)timer; // 避免未使用参数警告

    static uint32_t last_update = 0;
    uint32_t current_time = lv_tick_get();

    if(current_time - last_update > 5000) { // 每5秒更新一次
        last_update = current_time;

        // 模拟数据变化
        g_sensor_data.dose_rate += (rand() % 100 - 50) * 0.01f;
        if(g_sensor_data.dose_rate < 0) g_sensor_data.dose_rate = 0;

        g_sensor_data.heart_rate = 60 + rand() % 40;
        g_sensor_data.sao2 = 95 + rand() % 5;
        g_sensor_data.battery_level = 20 + (current_time / 1000) % 80;
        g_sensor_data.csq = 15 + rand() % 20;
        g_sensor_data.gps_satellites = 4 + rand() % 8;

        // 如果当前在主屏幕，重新创建以更新显示
        if(current_screen_type == SCREEN_HOME) {
            switch_to_screen(SCREEN_HOME);
        }
    }
}

static void time_timer_cb(lv_timer_t* timer)
{
    (void)timer; // 避免未使用参数警告
    update_time_display();
}

// 主GUI初始化函数
void my_gui(void)
{
    // 初始化随机数种子
    srand(time(NULL));

    // 创建启动屏幕
    switch_to_screen(SCREEN_START);

    // 创建定时器用于更新显示
    lv_timer_create(update_timer_cb, 1000, NULL);
    lv_timer_create(time_timer_cb, 1000, NULL);
}
