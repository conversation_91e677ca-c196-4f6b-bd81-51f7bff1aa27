#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <math.h>
#include <SDL2/SDL.h>
#include "lvgl/lvgl.h"
#include "lvgl_user/ui_app/ui.h"

#define WINDOW_WIDTH  240
#define WINDOW_HEIGHT 240

// SDL相关变量
static SDL_Window *window;
static SDL_Renderer *renderer;
static lv_disp_draw_buf_t draw_buf;
static lv_color_t buf1[WINDOW_WIDTH * 10];
static lv_color_t buf2[WINDOW_WIDTH * 10];

// 显示驱动刷新回调
static void sdl_display_flush(lv_disp_drv_t *disp_drv, const lv_area_t *area, lv_color_t *color_p)
{
    int32_t x, y;
    for (y = area->y1; y <= area->y2; y++) {
        for (x = area->x1; x <= area->x2; x++) {
            // 从LVGL颜色格式转换为SDL颜色
            uint8_t r = (LV_COLOR_GET_R(*color_p) * 255) / ((1 << LV_COLOR_DEPTH) - 1);
            uint8_t g = (LV_COLOR_GET_G(*color_p) * 255) / ((1 << LV_COLOR_DEPTH) - 1);  
            uint8_t b = (LV_COLOR_GET_B(*color_p) * 255) / ((1 << LV_COLOR_DEPTH) - 1);
            
            SDL_SetRenderDrawColor(renderer, r, g, b, SDL_ALPHA_OPAQUE);
            SDL_RenderDrawPoint(renderer, x, y);
            color_p++;
        }
    }
    lv_disp_flush_ready(disp_drv);
}

// 输入设备读取回调（鼠标）
static void sdl_mouse_read(lv_indev_drv_t *indev_drv, lv_indev_data_t *data)
{
    (void)indev_drv; // 消除未使用参数警告
    
    int mouse_x, mouse_y;
    Uint32 mouse_state = SDL_GetMouseState(&mouse_x, &mouse_y);
    
    if (mouse_state & SDL_BUTTON_LMASK) {
        data->state = LV_INDEV_STATE_PRESSED;
    } else {
        data->state = LV_INDEV_STATE_RELEASED;
    }
    
    data->point.x = mouse_x;
    data->point.y = mouse_y;
}

// 初始化SDL
static int init_sdl(void)
{
    if (SDL_Init(SDL_INIT_VIDEO) != 0) {
        fprintf(stderr, "SDL初始化失败: %s\n", SDL_GetError());
        return -1;
    }

    window = SDL_CreateWindow("LVGL手表模拟器",
                              SDL_WINDOWPOS_UNDEFINED, SDL_WINDOWPOS_UNDEFINED,
                              WINDOW_WIDTH, WINDOW_HEIGHT, SDL_WINDOW_SHOWN);
    if (!window) {
        fprintf(stderr, "创建窗口失败: %s\n", SDL_GetError());
        SDL_Quit();
        return -1;
    }

    renderer = SDL_CreateRenderer(window, -1, SDL_RENDERER_ACCELERATED);
    if (!renderer) {
        fprintf(stderr, "创建渲染器失败: %s\n", SDL_GetError());
        SDL_DestroyWindow(window);
        SDL_Quit();
        return -1;
    }

    return 0;
}

// 初始化LVGL显示驱动
static void init_lvgl_display(void)
{
    lv_disp_draw_buf_init(&draw_buf, buf1, buf2, WINDOW_WIDTH * 10);

    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = WINDOW_WIDTH;
    disp_drv.ver_res = WINDOW_HEIGHT;
    disp_drv.flush_cb = sdl_display_flush;
    disp_drv.draw_buf = &draw_buf;
    lv_disp_drv_register(&disp_drv);
}

// 初始化LVGL输入设备
static void init_lvgl_input(void)
{
    static lv_indev_drv_t indev_drv;
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = sdl_mouse_read;
    lv_indev_drv_register(&indev_drv);
}

// 模拟系统变量和函数（替换硬件相关）
typedef struct {
    int year, month, day, hour, min, sec;
} beijing_time_t;

typedef struct {
    float dose_rate;
    int cnt_now;
    float cps;
} csi_sensor_t;

// 模拟全局变量
uint32_t sys_unix_time = 1640995200; // 2022-01-01 00:00:00
uint8_t dev_battery_prt = 75;
uint8_t charger_state = 0;
csi_sensor_t csi_sensor = {0.25f, 123, 2.1f};

// 模拟系统设置结构
typedef struct {
    uint8_t bt_switch;
    uint8_t fly_mode_switch;
    uint8_t gnss_switch;
    uint8_t auto_upsend_switch;
} system_setings_t;

typedef struct {
    uint8_t watch_power_state;
    uint8_t manual_upsend_switch;
} system_state_t;

system_setings_t system_setings = {0, 0, 1, 1};
system_state_t system_state = {3, 0};

// 模拟EC800M结构
typedef struct {
    uint8_t upsend_state;
    uint8_t csq;
    uint8_t gnss_star_num;
} ec800m_t;

typedef struct {
    uint8_t now_state;
} ec800m_mc_t;

#define POWER_OFF 0
#define POWER_OFF_ing 1
#define IDEL 2

ec800m_t default_ec800m = {0, 25, 8};
ec800m_mc_t default_ec800m_mc = {IDEL};

uint8_t pm_touch_state = 0;

// 模拟函数实现
void get_beijing_time(beijing_time_t *t, uint32_t unix_time) {
    time_t time_val = unix_time;
    struct tm *tm_info = localtime(&time_val);
    t->year = tm_info->tm_year + 1900;
    t->month = tm_info->tm_mon + 1;
    t->day = tm_info->tm_mday;
    t->hour = tm_info->tm_hour;
    t->min = tm_info->tm_min;
    t->sec = tm_info->tm_sec;
}

uint32_t get_tmdelay_mstime(void) {
    return SDL_GetTicks();
}

uint8_t get_userkey(void) { return 1; }
uint8_t sacn_userkey(void) { return 0; }
void system_settings_saved(void) { printf("系统设置已保存\n"); }
void pm_sleep_in(void) { printf("屏幕熄灭\n"); }
void pm_sleep_out(void) { printf("屏幕点亮\n"); }
void system_wdog_feed(int x) { (void)x; }
void pm_fill_color(int color) { (void)color; }
void EnterCodeUpgrade(void) { printf("进入升级模式\n"); }

// 模拟用户显示更新函数
void user_display_updata(void) {
    // 简化的显示更新逻辑
    static uint32_t timer = 0;
    uint32_t current_time = get_tmdelay_mstime();
    
    if (current_time - timer >= 1000) {
        timer = current_time;
        
        // 更新系统时间
        sys_unix_time = time(NULL);
        
        // 模拟传感器数据变化
        static uint32_t sensor_timer = 0;
        if (current_time - sensor_timer > 5000) {
            sensor_timer = current_time;
            csi_sensor.dose_rate += (rand() % 100 - 50) * 0.01f;
            if (csi_sensor.dose_rate < 0) csi_sensor.dose_rate = 0;
            dev_battery_prt = 20 + (current_time / 1000) % 80;
        }
    }
}

int main(int argc, char *argv[])
{
    (void)argc; // 消除未使用参数警告
    (void)argv;
    
    printf("启动LVGL手表模拟器...\n");

    // 初始化SDL
    if (init_sdl() != 0) {
        return -1;
    }

    // 初始化LVGL
    lv_init();
    init_lvgl_display();
    init_lvgl_input();

    // 初始化UI
    ui_init();

    printf("模拟器启动成功！窗口大小: %dx%d\n", WINDOW_WIDTH, WINDOW_HEIGHT);
    printf("鼠标点击模拟触摸操作\n");
    printf("关闭窗口或按ESC键退出\n");

    // 主循环
    SDL_Event e;
    int quit = 0;
    uint32_t last_tick = SDL_GetTicks();
    
    while (!quit) {
        // 处理SDL事件
        while (SDL_PollEvent(&e)) {
            if (e.type == SDL_QUIT) {
                quit = 1;
            } else if (e.type == SDL_KEYDOWN) {
                if (e.key.keysym.sym == SDLK_ESCAPE) {
                    quit = 1;
                }
            }
        }

        // 更新LVGL
        uint32_t current_tick = SDL_GetTicks();
        lv_tick_inc(current_tick - last_tick);
        last_tick = current_tick;
        
        lv_timer_handler();

        // 更新显示逻辑
        user_display_updata();

        // 清除屏幕
        SDL_SetRenderDrawColor(renderer, 0, 0, 0, SDL_ALPHA_OPAQUE);
        SDL_RenderClear(renderer);
        
        // 渲染
        SDL_RenderPresent(renderer);
        
        // 控制帧率
        SDL_Delay(5);
    }

    // 清理资源
    SDL_DestroyRenderer(renderer);
    SDL_DestroyWindow(window);
    SDL_Quit();

    printf("模拟器已退出\n");
    return 0;
} 