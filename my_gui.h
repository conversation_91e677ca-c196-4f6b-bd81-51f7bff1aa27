#ifndef MY_GUI_H
#define MY_GUI_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl/lvgl.h"
#include <stdbool.h>

/**
 * @brief 初始化并启动GUI界面
 * 
 * 这个函数会创建一个完整的手表界面，包含：
 * - 启动屏幕：显示应用名称和版本信息
 * - 主屏幕：显示时间、电池、心率、辐射剂量等信息
 * - 设置屏幕：包含蓝牙、飞行模式、GPS等开关设置
 * 
 * 操作说明：
 * - 点击启动屏幕进入主屏幕
 * - 长按主屏幕进入设置屏幕
 * - 在设置屏幕点击Back按钮返回主屏幕
 * - 各种开关可以点击切换状态
 * 
 * 功能特性：
 * - 实时时间显示
 * - 模拟传感器数据（每5秒更新）
 * - 电池电量显示（弧形进度条）
 * - 心率监测显示
 * - 辐射剂量率显示
 * - 系统设置管理
 * - 网络信号强度和GPS卫星数量显示
 */
void my_gui(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /* MY_GUI_H */
