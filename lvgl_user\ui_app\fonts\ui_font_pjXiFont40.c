/*******************************************************************************
 * Size: 40 px
 * Bpp: 2
 * Opts: --bpp 2 --size 40 --font D:\Work\LVGL\LYwatch\assets\獅尾肉丸-Light.ttf -o D:\Work\LVGL\LYwatch\assets\ui_font_pjXiFont40.c --format lvgl --symbols 0123456789.umSv/h蓝牙开关飞行模式上报主动自信号卫星 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_PJXIFONT40
#define UI_FONT_PJXIFONT40 1
#endif

#if UI_FONT_PJXIFONT40

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+002E "." */
    0x0, 0xf, 0xcb, 0xfb, 0xff, 0x7f, 0x41, 0x0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x80, 0x0, 0x0, 0x2c, 0x0,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x38, 0x0, 0x0,
    0xb, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0xe,
    0x0, 0x0, 0x1, 0xd0, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x3, 0x80, 0x0, 0x0, 0x74, 0x0, 0x0,
    0xb, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x1d,
    0x0, 0x0, 0x2, 0xc0, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x7, 0x80, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x2d,
    0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x7, 0x40, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x1, 0xd0, 0x0, 0x0, 0x2c,
    0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x78, 0x0,
    0x0, 0xb, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0xe, 0x0, 0x0, 0x2, 0xd0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x3, 0x80, 0x0, 0x0, 0x74, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x1, 0x50, 0x0, 0x0, 0x2, 0xff, 0xf4,
    0x0, 0x0, 0xf8, 0x7, 0xe0, 0x0, 0x3e, 0x0,
    0xf, 0x40, 0xb, 0xc0, 0x0, 0x7c, 0x0, 0xf4,
    0x0, 0x3, 0xe0, 0x2f, 0x0, 0x0, 0x2f, 0x3,
    0xe0, 0x0, 0x1, 0xf4, 0x7d, 0x0, 0x0, 0xf,
    0x87, 0xd0, 0x0, 0x0, 0xfc, 0xbc, 0x0, 0x0,
    0xf, 0xcf, 0xc0, 0x0, 0x0, 0xbc, 0xfc, 0x0,
    0x0, 0xb, 0xdf, 0xc0, 0x0, 0x0, 0xbd, 0xfc,
    0x0, 0x0, 0xb, 0xdf, 0xc0, 0x0, 0x0, 0x7d,
    0xfc, 0x0, 0x0, 0xb, 0xdf, 0xc0, 0x0, 0x0,
    0xbd, 0xfc, 0x0, 0x0, 0xb, 0xdf, 0xc0, 0x0,
    0x0, 0xbc, 0xbc, 0x0, 0x0, 0xb, 0xc7, 0xd0,
    0x0, 0x0, 0xfc, 0x3d, 0x0, 0x0, 0xf, 0x83,
    0xe0, 0x0, 0x1, 0xf4, 0x2f, 0x0, 0x0, 0x2f,
    0x0, 0xf4, 0x0, 0x3, 0xe0, 0xb, 0xc0, 0x0,
    0xbc, 0x0, 0x3e, 0x0, 0xf, 0x40, 0x0, 0xf8,
    0x7, 0xd0, 0x0, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0x1, 0x50, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x40, 0x0, 0x0, 0x2f, 0xe0, 0x0,
    0x1b, 0xff, 0xe0, 0x0, 0x3e, 0x97, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x15, 0xaf, 0xfa, 0x94, 0xbf, 0xff, 0xff, 0xff,

    /* U+0032 "2" */
    0x0, 0x5, 0x54, 0x0, 0x0, 0x1f, 0xff, 0xf8,
    0x0, 0xb, 0xd0, 0xb, 0xf0, 0x2, 0xf4, 0x0,
    0x1f, 0xc0, 0x7f, 0x0, 0x0, 0xbd, 0xb, 0xe0,
    0x0, 0x3, 0xf0, 0xfc, 0x0, 0x0, 0x3f, 0x2,
    0x0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x0, 0x2f,
    0x0, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x0,
    0x3f, 0x0, 0x0, 0x0, 0x7, 0xd0, 0x0, 0x0,
    0x0, 0xbc, 0x0, 0x0, 0x0, 0xf, 0x40, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x0, 0xbc, 0x0,
    0x0, 0x0, 0x1f, 0x0, 0x0, 0x0, 0x3, 0xc0,
    0x0, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0x2d,
    0x0, 0x0, 0x0, 0xb, 0x80, 0x0, 0x0, 0x1,
    0xe0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0,
    0x0, 0x7, 0xea, 0xaa, 0xaa, 0xa9, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xfe,

    /* U+0033 "3" */
    0x0, 0x1, 0x55, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x80, 0x0, 0x1f, 0x40, 0x1f, 0xd0, 0x1, 0xf8,
    0x0, 0xf, 0xc0, 0xf, 0xd0, 0x0, 0xf, 0x80,
    0x3f, 0x0, 0x0, 0x2f, 0x0, 0xf4, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0x1, 0xf4, 0x0, 0x0,
    0x0, 0x7, 0xd0, 0x0, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0x0, 0x0, 0xf8, 0x0, 0x0, 0x0, 0xb,
    0xc0, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0xff, 0xf4, 0x0, 0x0,
    0x1, 0xab, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xbe,
    0x0, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0,
    0x0, 0xbd, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x0, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x0, 0xb,
    0xc0, 0x0, 0x0, 0x0, 0x2f, 0x40, 0x0, 0x0,
    0x0, 0xbc, 0x3e, 0x0, 0x0, 0x3, 0xf0, 0xfd,
    0x0, 0x0, 0x1f, 0x82, 0xf8, 0x0, 0x0, 0xfc,
    0x3, 0xf0, 0x0, 0xb, 0xd0, 0x3, 0xe0, 0x1,
    0xfd, 0x0, 0x2, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x15, 0x40, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x0,
    0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0xe, 0xbc,
    0x0, 0x0, 0x0, 0x2c, 0xbc, 0x0, 0x0, 0x0,
    0x70, 0xbc, 0x0, 0x0, 0x0, 0xd0, 0xbc, 0x0,
    0x0, 0x3, 0x80, 0xbc, 0x0, 0x0, 0xb, 0x0,
    0xbc, 0x0, 0x0, 0xd, 0x0, 0xbc, 0x0, 0x0,
    0x38, 0x0, 0xbc, 0x0, 0x0, 0xb0, 0x0, 0xbc,
    0x0, 0x1, 0xd0, 0x0, 0xbc, 0x0, 0x3, 0x80,
    0x0, 0xbc, 0x0, 0xb, 0x0, 0x0, 0xbc, 0x0,
    0x1c, 0x0, 0x0, 0xbc, 0x0, 0x38, 0x0, 0x0,
    0xbc, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0,
    0x0, 0xbc, 0x0, 0x0, 0x0, 0x0, 0xbc, 0x0,
    0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0x0, 0x0, 0x0, 0x0, 0xbc,
    0x0,

    /* U+0035 "5" */
    0x1, 0xff, 0xff, 0xff, 0xe0, 0x7, 0xff, 0xff,
    0xff, 0x80, 0x1e, 0xaa, 0xaa, 0xa9, 0x0, 0x70,
    0x0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x0, 0x0,
    0xb, 0x0, 0x0, 0x0, 0x0, 0x2c, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x3, 0x80,
    0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x0, 0x0, 0x0, 0x0, 0xe6, 0xff, 0xe0,
    0x0, 0x3, 0xff, 0xff, 0xf8, 0x0, 0x9, 0x0,
    0x7, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xfc, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xf0, 0x0, 0x0, 0x0, 0xf, 0xc0, 0x0, 0x0,
    0x0, 0x2f, 0x0, 0x0, 0x0, 0x0, 0xbd, 0x0,
    0x0, 0x0, 0x2, 0xf4, 0x0, 0x0, 0x0, 0xb,
    0xc0, 0x0, 0x0, 0x0, 0x2f, 0xf, 0x40, 0x0,
    0x0, 0xfc, 0x3f, 0x0, 0x0, 0x7, 0xd0, 0xfd,
    0x0, 0x0, 0x3f, 0x1, 0xf8, 0x0, 0x3, 0xf0,
    0x1, 0xf4, 0x0, 0xbf, 0x0, 0x0, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x15, 0x50, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x6, 0x90, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x0,
    0x2f, 0x40, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x0,
    0x0, 0x3e, 0x0, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x1, 0xf0,
    0x0, 0x0, 0x0, 0xf, 0x80, 0x0, 0x0, 0x0,
    0x7c, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x0,
    0x0, 0xf, 0x40, 0xaf, 0x90, 0x0, 0x7d, 0x2f,
    0xff, 0xf4, 0x2, 0xf7, 0xd0, 0xb, 0xf4, 0xf,
    0xf4, 0x0, 0x3, 0xf0, 0x3f, 0x0, 0x0, 0x7,
    0xe0, 0xfc, 0x0, 0x0, 0xf, 0xc3, 0xf0, 0x0,
    0x0, 0x2f, 0x4f, 0xc0, 0x0, 0x0, 0x7d, 0x3f,
    0x0, 0x0, 0x1, 0xf4, 0xfc, 0x0, 0x0, 0x7,
    0xd2, 0xf0, 0x0, 0x0, 0x1f, 0x47, 0xd0, 0x0,
    0x0, 0x7c, 0xf, 0x80, 0x0, 0x2, 0xf0, 0x2f,
    0x0, 0x0, 0xf, 0x80, 0x3e, 0x0, 0x0, 0xbc,
    0x0, 0x7d, 0x0, 0x7, 0xd0, 0x0, 0x7e, 0x0,
    0xbd, 0x0, 0x0, 0x3f, 0xff, 0xc0, 0x0, 0x0,
    0x5, 0x50, 0x0, 0x0,

    /* U+0037 "7" */
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xfc, 0x6a, 0xaa, 0xaa, 0xab, 0xc0, 0x0, 0x0,
    0x0, 0x38, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x1d, 0x0,
    0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0, 0x78,
    0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x2d, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0x78, 0x0, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x0, 0x1, 0xf0, 0x0,
    0x0, 0x0, 0x3e, 0x0, 0x0, 0x0, 0x3, 0xc0,
    0x0, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0, 0xf,
    0x40, 0x0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x0, 0x7, 0xd0, 0x0, 0x0,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0x1f, 0x40, 0x0,
    0x0, 0x2, 0xf0, 0x0, 0x0, 0x0, 0x3e, 0x0,
    0x0, 0x0, 0xb, 0xd0, 0x0, 0x0, 0x0, 0xfc,
    0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x1, 0x54, 0x0, 0x0, 0x7, 0xff, 0xf8,
    0x0, 0x2, 0xf4, 0x7, 0xf0, 0x0, 0xbc, 0x0,
    0xb, 0xc0, 0x1f, 0x0, 0x0, 0x3e, 0x2, 0xe0,
    0x0, 0x2, 0xf0, 0x3e, 0x0, 0x0, 0x1f, 0x3,
    0xe0, 0x0, 0x1, 0xf0, 0x3e, 0x0, 0x0, 0x2f,
    0x3, 0xf0, 0x0, 0x2, 0xe0, 0x1f, 0x80, 0x0,
    0x3c, 0x0, 0xfd, 0x0, 0xb, 0x80, 0x7, 0xf4,
    0x2, 0xe0, 0x0, 0x1f, 0xf4, 0xb4, 0x0, 0x0,
    0x7f, 0xfc, 0x0, 0x0, 0x0, 0xff, 0xf4, 0x0,
    0x0, 0x7d, 0xbf, 0xe0, 0x0, 0x2e, 0x0, 0xbf,
    0xc0, 0xb, 0x80, 0x0, 0xff, 0x2, 0xe0, 0x0,
    0x3, 0xf4, 0x3c, 0x0, 0x0, 0xf, 0xcb, 0xc0,
    0x0, 0x0, 0xbc, 0xf8, 0x0, 0x0, 0x7, 0xdf,
    0x80, 0x0, 0x0, 0x7d, 0xfc, 0x0, 0x0, 0x7,
    0xcb, 0xc0, 0x0, 0x0, 0xbc, 0x7e, 0x0, 0x0,
    0xf, 0x82, 0xf4, 0x0, 0x3, 0xf0, 0xb, 0xe0,
    0x1, 0xf8, 0x0, 0x1f, 0xff, 0xfd, 0x0, 0x0,
    0x5, 0x54, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x1, 0x54, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x40, 0x0, 0x1f, 0xd0, 0x2f, 0x80, 0x1, 0xf8,
    0x0, 0x1f, 0x80, 0xf, 0xc0, 0x0, 0x2f, 0x0,
    0x7c, 0x0, 0x0, 0x3f, 0x3, 0xf0, 0x0, 0x0,
    0xbc, 0xf, 0x80, 0x0, 0x1, 0xf8, 0x7e, 0x0,
    0x0, 0x3, 0xf1, 0xf4, 0x0, 0x0, 0xf, 0xc7,
    0xd0, 0x0, 0x0, 0x3f, 0x1f, 0x80, 0x0, 0x0,
    0xfc, 0x3f, 0x0, 0x0, 0x3, 0xf0, 0xfc, 0x0,
    0x0, 0xf, 0xc1, 0xfc, 0x0, 0x0, 0x3f, 0x2,
    0xf8, 0x0, 0x3, 0xfc, 0x3, 0xfd, 0x0, 0xbf,
    0xe0, 0x2, 0xff, 0xff, 0x9f, 0x40, 0x0, 0x6f,
    0x90, 0xbc, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0x1f, 0x40, 0x0, 0x0, 0x0, 0xfc,
    0x0, 0x0, 0x0, 0x7, 0xd0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x0,
    0x0, 0x3f, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x1f, 0xd0,
    0x0, 0x0, 0x7, 0xf4, 0x0, 0x0, 0x0, 0xb9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0053 "S" */
    0x0, 0x1, 0x55, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xe0, 0x0, 0x2f, 0x90, 0x7, 0xf4, 0x2, 0xf4,
    0x0, 0x3, 0xf0, 0x1f, 0x0, 0x0, 0xb, 0xc0,
    0xf8, 0x0, 0x0, 0x2f, 0x3, 0xd0, 0x0, 0x0,
    0x78, 0xf, 0x40, 0x0, 0x0, 0xe0, 0x3e, 0x0,
    0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x3,
    0xf4, 0x0, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0,
    0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0xf, 0xfe,
    0x40, 0x0, 0x0, 0xb, 0xff, 0xd0, 0x0, 0x0,
    0x2, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x1f, 0xfd, 0x0, 0x0, 0x0,
    0x7, 0xfc, 0x0, 0x0, 0x0, 0x3, 0xf8, 0x0,
    0x0, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x0, 0xb,
    0xc6, 0x0, 0x0, 0x0, 0x1f, 0x3c, 0x0, 0x0,
    0x0, 0x7c, 0xf4, 0x0, 0x0, 0x2, 0xf3, 0xe0,
    0x0, 0x0, 0xf, 0x8f, 0x80, 0x0, 0x0, 0xbc,
    0x3f, 0x0, 0x0, 0xb, 0xd0, 0x7f, 0x90, 0x6,
    0xfd, 0x0, 0x1f, 0xff, 0xff, 0x40, 0x0, 0x1,
    0x55, 0x40, 0x0, 0x0,

    /* U+0068 "h" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfc, 0x0, 0x15, 0x40, 0x0, 0x0, 0xfc,
    0x7, 0xff, 0xf4, 0x0, 0x0, 0xfc, 0x2f, 0xff,
    0xfc, 0x0, 0x0, 0xfc, 0xf8, 0x0, 0xbe, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0x3f, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x2f, 0x0, 0x0, 0xfc, 0x0, 0x0,
    0x1f, 0x40, 0x0, 0xfc, 0x0, 0x0, 0x1f, 0x40,
    0x0, 0xfc, 0x0, 0x0, 0xf, 0x40, 0x0, 0xfc,
    0x0, 0x0, 0xf, 0x40, 0x0, 0xfc, 0x0, 0x0,
    0xf, 0x40, 0x0, 0xfc, 0x0, 0x0, 0xf, 0x40,
    0x0, 0xfc, 0x0, 0x0, 0xf, 0x40, 0x0, 0xfc,
    0x0, 0x0, 0xf, 0x40, 0x0, 0xfc, 0x0, 0x0,
    0x1f, 0x40, 0x0, 0xfc, 0x0, 0x0, 0x1f, 0x40,
    0x0, 0xfc, 0x0, 0x0, 0x1f, 0x40, 0x0, 0xfc,
    0x0, 0x0, 0x1f, 0x40, 0x0, 0xfc, 0x0, 0x0,
    0x1f, 0x40, 0x5, 0xfd, 0x40, 0x1, 0x6f, 0xd4,
    0x7f, 0xff, 0xf0, 0xf, 0xff, 0xfe,

    /* U+006D "m" */
    0x0, 0x0, 0x0, 0x15, 0x0, 0x0, 0x5, 0x40,
    0x0, 0x1, 0xbe, 0x1, 0xff, 0xf8, 0x0, 0x7f,
    0xfe, 0x0, 0x7, 0xff, 0xc2, 0xfe, 0xff, 0xc0,
    0xff, 0xbf, 0xf0, 0x1, 0xab, 0xf3, 0xe0, 0x3,
    0xf4, 0xf4, 0x0, 0xfd, 0x0, 0x0, 0xbf, 0xc0,
    0x0, 0x7f, 0xe0, 0x0, 0xf, 0x80, 0x0, 0x2f,
    0x80, 0x0, 0xf, 0xe0, 0x0, 0x3, 0xf0, 0x0,
    0xb, 0xc0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0xbc,
    0x0, 0x2, 0xf0, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x2f, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0xb, 0xc0, 0x0, 0x2f, 0x0, 0x0, 0xb,
    0xc0, 0x0, 0x2, 0xf0, 0x0, 0xb, 0xc0, 0x0,
    0x2, 0xf0, 0x0, 0x0, 0xbc, 0x0, 0x2, 0xf0,
    0x0, 0x0, 0xbc, 0x0, 0x0, 0x2f, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0x2f, 0x0, 0x0, 0xb, 0xc0,
    0x0, 0x2f, 0x0, 0x0, 0xb, 0xc0, 0x0, 0x2,
    0xf0, 0x0, 0xb, 0xc0, 0x0, 0x2, 0xf0, 0x0,
    0x0, 0xbc, 0x0, 0x2, 0xf0, 0x0, 0x0, 0xbc,
    0x0, 0x0, 0x2f, 0x0, 0x0, 0xbc, 0x0, 0x0,
    0x2f, 0x0, 0x0, 0xb, 0xc0, 0x0, 0x2f, 0x0,
    0x0, 0xb, 0xc0, 0x0, 0x2, 0xf0, 0x0, 0xb,
    0xc0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0xbc, 0x0,
    0x17, 0xf9, 0x0, 0x5, 0xfe, 0x40, 0x1, 0x7f,
    0x90, 0x3f, 0xff, 0xf4, 0xf, 0xff, 0xfd, 0x3,
    0xff, 0xff, 0x40,

    /* U+0075 "u" */
    0x16, 0xa0, 0x0, 0x5, 0x6a, 0x2, 0xff, 0xd0,
    0x0, 0x3f, 0xfc, 0x0, 0x2f, 0x40, 0x0, 0x7,
    0xf0, 0x0, 0x7d, 0x0, 0x0, 0xf, 0xc0, 0x1,
    0xf4, 0x0, 0x0, 0x3e, 0x0, 0x7, 0xd0, 0x0,
    0x0, 0xf8, 0x0, 0x1f, 0x0, 0x0, 0x3, 0xe0,
    0x0, 0x7c, 0x0, 0x0, 0xf, 0x80, 0x1, 0xf0,
    0x0, 0x0, 0x3e, 0x0, 0x7, 0xc0, 0x0, 0x0,
    0xf8, 0x0, 0x1f, 0x0, 0x0, 0x3, 0xe0, 0x0,
    0x7c, 0x0, 0x0, 0xf, 0x80, 0x1, 0xf0, 0x0,
    0x0, 0x3e, 0x0, 0x7, 0xc0, 0x0, 0x0, 0xf8,
    0x0, 0x1f, 0x40, 0x0, 0x3, 0xe0, 0x0, 0x7d,
    0x0, 0x0, 0xf, 0x80, 0x0, 0xf8, 0x0, 0x0,
    0xbe, 0x0, 0x3, 0xf0, 0x0, 0xf, 0xf8, 0x0,
    0xb, 0xf0, 0x2, 0xf3, 0xe0, 0x0, 0xf, 0xff,
    0xfe, 0xf, 0xe5, 0x0, 0xf, 0xff, 0xd0, 0x2f,
    0xfc, 0x0, 0x5, 0x50, 0x0, 0x0, 0x0,

    /* U+0076 "v" */
    0x3f, 0xff, 0xf4, 0xb, 0xff, 0xf4, 0x5b, 0xf9,
    0x40, 0x5, 0xbe, 0x40, 0xf, 0xc0, 0x0, 0x1,
    0xd0, 0x0, 0x2f, 0x40, 0x0, 0xb, 0x0, 0x0,
    0x3f, 0x0, 0x0, 0x38, 0x0, 0x0, 0xfc, 0x0,
    0x1, 0xc0, 0x0, 0x1, 0xf8, 0x0, 0xb, 0x0,
    0x0, 0x3, 0xf0, 0x0, 0x34, 0x0, 0x0, 0xb,
    0xd0, 0x1, 0xc0, 0x0, 0x0, 0xf, 0x80, 0xe,
    0x0, 0x0, 0x0, 0x2f, 0x0, 0x34, 0x0, 0x0,
    0x0, 0x7d, 0x2, 0xc0, 0x0, 0x0, 0x0, 0xfc,
    0xe, 0x0, 0x0, 0x0, 0x1, 0xf4, 0x70, 0x0,
    0x0, 0x0, 0x3, 0xe2, 0x80, 0x0, 0x0, 0x0,
    0xb, 0xcd, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x7c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0,

    /* U+4E0A "上" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xc0, 0x0, 0x2, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0x55, 0x55, 0x55,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x1, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0xbf, 0x81, 0x55, 0x55, 0x55, 0x57, 0xd5, 0x55,
    0x55, 0x5f, 0xfc, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+4E3B "主" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0x40, 0x0, 0x19, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x7f, 0xe0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x5, 0x55, 0x55, 0x5f, 0x55, 0x55,
    0x55, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0,
    0x7f, 0x40, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0xf, 0xfc, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4FE1 "信" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x0, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0x0, 0x0,
    0x7, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xc0,
    0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0x0, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x0, 0xb, 0xc0, 0x0,
    0x50, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x78,
    0x0, 0x2f, 0xc0, 0x0, 0x2e, 0x5, 0x55, 0x55,
    0x55, 0x55, 0x57, 0xfe, 0x0, 0x3, 0xd1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0xbc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x40, 0x0, 0x3f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfc, 0x0, 0x7, 0xf0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xff, 0x0,
    0x1, 0x55, 0x55, 0x55, 0x55, 0x50, 0x0, 0x1d,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x70, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x80, 0xd, 0xf, 0x0, 0x1, 0x55,
    0x55, 0x55, 0x57, 0xfc, 0x2, 0x80, 0xf0, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x60, 0xf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x50, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xc0, 0x0, 0xf, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0xf0,
    0x0, 0x3c, 0x0, 0x0, 0x0, 0x1f, 0x40, 0x0,
    0xf, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x1, 0xf0,
    0x0, 0x0, 0xf0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0x1f, 0x0, 0x0, 0xf, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x1, 0xf0, 0x0, 0x0, 0xf0, 0x0, 0x3c,
    0x0, 0x0, 0x0, 0x1f, 0x0, 0x0, 0xf, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0xf0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x1f, 0x0,
    0x0, 0xf, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x1,
    0xf0, 0x0, 0x0, 0xf0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0x0, 0x3, 0xc0,
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0xf0, 0x0,
    0x3c, 0x0, 0x0, 0x0, 0x1f, 0x0, 0x0, 0xf,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x1, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5173 "关" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x40, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x40, 0x0,
    0x0, 0x2f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e,
    0x0, 0x0, 0x3, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xf8, 0x0, 0x0, 0xbc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xd0, 0x0, 0xf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0x0, 0x2, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf0, 0x0,
    0x38, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa,
    0x0, 0xf, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xd0, 0x3, 0xff, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf0, 0x0,
    0x0, 0x1, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xf0, 0x0, 0x0, 0x1f, 0xf8, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x3, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xca,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf4, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x2, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xd0, 0xe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbc, 0x0, 0xb8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x0, 0x3,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xc0,
    0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xf0, 0x0, 0x0, 0x3f, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xf8, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xbd, 0x0, 0x0, 0x0, 0x3, 0xfe,
    0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0x40, 0x0, 0x7e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xd0, 0xb9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x64, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x0,

    /* U+52A8 "动" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xf0, 0x0, 0xf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xc0, 0x0, 0xf4, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xfc, 0x0, 0xf, 0x40, 0x0,
    0x0, 0x1, 0x55, 0x55, 0x55, 0x0, 0x0, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0xa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x40, 0x3, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x2, 0xe0, 0x0, 0x0, 0x0, 0x3, 0xf4, 0x0,
    0xf0, 0x0, 0x2d, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xc0, 0xf, 0x0, 0x2, 0xd0, 0xbf, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xf0, 0x0, 0x2d, 0x0, 0x0,
    0x14, 0x0, 0x0, 0x0, 0xf, 0x0, 0x2, 0xd0,
    0x0, 0x2, 0xe0, 0x0, 0x0, 0x1, 0xf0, 0x0,
    0x2d, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x0, 0x1e,
    0x0, 0x3, 0xd0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x2, 0xe0, 0x0, 0x3d, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0x0, 0x2d, 0x0, 0x3, 0xd0, 0x0, 0xf,
    0x0, 0x40, 0x0, 0x3, 0xc0, 0x0, 0x3c, 0x0,
    0x1, 0xe0, 0xd, 0x0, 0x0, 0x3c, 0x0, 0x3,
    0xc0, 0x0, 0x3c, 0x0, 0x74, 0x0, 0x7, 0x80,
    0x0, 0x3c, 0x0, 0x7, 0x80, 0x3, 0xc0, 0x0,
    0xb4, 0x0, 0x3, 0xc0, 0x0, 0xf0, 0x0, 0x1f,
    0x0, 0xf, 0x0, 0x0, 0x3c, 0x0, 0x1d, 0x0,
    0x0, 0xf4, 0x1, 0xe0, 0x0, 0x3, 0xc0, 0x3,
    0x80, 0x0, 0xb, 0xc0, 0x2d, 0x0, 0x0, 0x7c,
    0x0, 0xb0, 0x0, 0x6, 0xfc, 0x3, 0xc0, 0x0,
    0x7, 0x80, 0x1d, 0x16, 0xbf, 0xe7, 0xd0, 0xb4,
    0x0, 0x0, 0x78, 0x3, 0xff, 0xfe, 0x40, 0x3d,
    0x1e, 0x0, 0x0, 0xb, 0x40, 0x1f, 0xf9, 0x0,
    0x3, 0xc3, 0xc0, 0x0, 0x0, 0xf4, 0x0, 0xd0,
    0x0, 0x0, 0x4, 0xb4, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0x0, 0x0, 0x1,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x7, 0x40, 0x1,
    0x0, 0x3e, 0x0, 0x0, 0x0, 0x0, 0x1, 0xd0,
    0x0, 0x2b, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xb4, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x28, 0x0, 0x0, 0x0, 0x3d, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+536B "卫" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0x0, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xd0, 0x0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0x0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xd0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xd0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xd0, 0x0,
    0x0, 0x1f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xd0, 0x0, 0x0, 0x1f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0x0, 0x0, 0x1, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x0, 0x2e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x0, 0x0,
    0x2, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xd0,
    0x0, 0x0, 0x3d, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xd0, 0x4, 0x0, 0xbc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0x0, 0x6f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x0,
    0x0, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xd0, 0x0,
    0x0, 0x0, 0x14, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0, 0x0,
    0x3, 0xd0, 0x0, 0x0, 0x1, 0xff, 0xc7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x15, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x0,

    /* U+53F7 "号" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfe, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x40, 0x0, 0x0, 0x3, 0xc0,
    0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x3d, 0x55,
    0x55, 0x55, 0x55, 0x7c, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x50,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfc, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x15, 0x55, 0x57,
    0xd5, 0x55, 0x55, 0x55, 0x55, 0x55, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x0, 0x0, 0x0, 0x2f, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x7,
    0xe5, 0x55, 0x55, 0x55, 0x55, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0,

    /* U+5F00 "开" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfd, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x2, 0xe0, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0x0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xe0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xe0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xe0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xe0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xe0, 0x0, 0x0, 0xf0, 0x0, 0x1f, 0x80, 0x0,
    0x0, 0x2e, 0x0, 0x0, 0xf, 0x0, 0x3, 0xfe,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x55, 0x55, 0x7e, 0x55, 0x55, 0x5f,
    0x55, 0x55, 0x54, 0x0, 0x0, 0x3, 0xd0, 0x0,
    0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xc0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb8, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2d, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb4, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x7, 0xc0,
    0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x0, 0x1e, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0x0, 0x0, 0x2, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0x0,

    /* U+5F0F "式" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0x2, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xd0, 0x7, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3d, 0x0, 0x1f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x7d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x0,
    0x1, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xd0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0x0, 0x0, 0xff, 0x83, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x40, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf, 0x40, 0x0,
    0x0, 0x0, 0x15, 0x55, 0x55, 0x5f, 0xf8, 0xb8,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0x47, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf4,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x3, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0x2e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x1, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf4, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0x0, 0x0, 0xf, 0x40,
    0x0, 0x0, 0xb8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x0, 0x2e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x50,
    0xb, 0xc0, 0x0, 0x40, 0x0, 0x0, 0xf4, 0x1,
    0xf8, 0x0, 0x3e, 0x0, 0x24, 0x0, 0x0, 0xf,
    0x5b, 0xe4, 0x0, 0x1, 0xf4, 0x3, 0x0, 0x0,
    0x1, 0xff, 0xd0, 0x0, 0x0, 0xb, 0xd0, 0x70,
    0x0, 0x1b, 0xff, 0x40, 0x0, 0x0, 0x0, 0x3f,
    0x8b, 0x0, 0xbf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf0, 0xf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x0, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+62A5 "报" */
    0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x90, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xfd, 0x0, 0x0, 0x3d, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xf, 0x40,
    0x3, 0xc0, 0x0, 0x0, 0xb, 0x40, 0x0, 0x3,
    0xd0, 0x0, 0xf0, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0xf4, 0x0, 0x3c, 0x0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x3d, 0x9, 0xf, 0x0, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0xf, 0x4f, 0xe3, 0xc0, 0x0,
    0x0, 0xf, 0x0, 0x1a, 0xab, 0xeb, 0xfc, 0xf0,
    0x0, 0x0, 0x7, 0x80, 0x6, 0xaa, 0xfa, 0xa9,
    0x3c, 0x0, 0x0, 0x2, 0xd0, 0x0, 0x0, 0x3d,
    0x0, 0xf, 0x0, 0xa, 0x96, 0xf0, 0x0, 0x0,
    0xf, 0x40, 0x3, 0xc0, 0x0, 0x1f, 0xf8, 0x0,
    0x0, 0x3, 0xd0, 0x0, 0xf0, 0x0, 0x1, 0xf8,
    0x0, 0x0, 0x0, 0xf4, 0x0, 0x3c, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0x0, 0xf, 0x0,
    0x0, 0x0, 0xb, 0x40, 0x0, 0xf, 0x40, 0x13,
    0xd5, 0x55, 0x55, 0x5b, 0xf0, 0x0, 0x3, 0xd0,
    0xb4, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xfa, 0xd0, 0x3c, 0x18, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0x3f, 0x80, 0xf, 0x3, 0x0, 0x0, 0x3d,
    0x0, 0x1, 0xbf, 0x40, 0x3, 0xc0, 0xd0, 0x0,
    0xf, 0x0, 0xb, 0xff, 0xd0, 0x0, 0xf0, 0x18,
    0x0, 0x7, 0xc0, 0xb, 0xf8, 0xf4, 0x0, 0x3c,
    0x3, 0x0, 0x3, 0xd0, 0x0, 0xe0, 0x3d, 0x0,
    0xf, 0x0, 0xa0, 0x0, 0xf0, 0x0, 0x10, 0xf,
    0x40, 0x3, 0xc0, 0xd, 0x0, 0xb8, 0x0, 0x0,
    0x3, 0xd0, 0x0, 0xf0, 0x2, 0xc0, 0x3c, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x3c, 0x0, 0x38, 0x2e,
    0x0, 0x0, 0x0, 0x3d, 0x0, 0xf, 0x0, 0xb,
    0x4f, 0x0, 0x0, 0x0, 0xf, 0x40, 0x3, 0xc0,
    0x0, 0xff, 0x80, 0x0, 0x0, 0x3, 0xd0, 0x0,
    0xf0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0xf4,
    0x0, 0x3c, 0x0, 0x3, 0xf4, 0x0, 0x0, 0x0,
    0x3d, 0x0, 0xf, 0x0, 0x3, 0xef, 0x40, 0x0,
    0x0, 0xf, 0x40, 0x3, 0xc0, 0x3, 0xd2, 0xf8,
    0x0, 0x0, 0x3, 0xd0, 0x0, 0xf0, 0x3, 0xd0,
    0x1f, 0xd0, 0x2, 0xaa, 0xf0, 0x0, 0x3c, 0x7,
    0xc0, 0x0, 0xff, 0x0, 0x7, 0xfc, 0x0, 0xf,
    0xb, 0x80, 0x0, 0x7, 0xfc, 0x0, 0x78, 0x0,
    0x3, 0xef, 0x40, 0x0, 0x0, 0x2d, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+661F "星" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x40, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x1f, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xe0, 0x0, 0x0, 0x7, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x1,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x0, 0x0,
    0x0, 0x7c, 0x0, 0x0, 0x0, 0x0, 0xf, 0x40,
    0x0, 0x0, 0x1f, 0x55, 0x55, 0x55, 0x55, 0x57,
    0xd0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0x0, 0x0, 0x0, 0x7c, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0, 0x1f,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x0,
    0x7, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x7c, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0x1f, 0x40, 0x0, 0x10,
    0x0, 0x3, 0x80, 0x0, 0x0, 0x1, 0x3c, 0x0,
    0x1f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x40, 0x7, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xc0, 0x1, 0xf0, 0x0, 0x1, 0xf8, 0x0,
    0x0, 0x7, 0xc0, 0x0, 0x7c, 0x0, 0x0, 0xff,
    0x40, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x1, 0xe5, 0x55, 0x57, 0xd5,
    0x55, 0x55, 0x50, 0x0, 0x0, 0xf0, 0x0, 0x1,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x7c, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x1f, 0x0, 0x0, 0x14, 0x0, 0x0,
    0xb0, 0x0, 0x0, 0x7, 0xc0, 0x0, 0x2f, 0xc0,
    0x0, 0xa0, 0x55, 0x55, 0x55, 0xf5, 0x55, 0x5f,
    0xf8, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7c, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0x0, 0x0, 0x0,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x7, 0xc0, 0x0,
    0x0, 0xbf, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6A21 "模" */
    0x0, 0x5, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0xe0, 0x0, 0x0, 0xf, 0x0, 0x0, 0xf4,
    0x0, 0x0, 0x1d, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0xf, 0x4b, 0xd0, 0x2, 0xd2, 0xf8, 0x0, 0xf,
    0x0, 0x0, 0xb9, 0xff, 0x0, 0x2d, 0x7f, 0xc0,
    0x0, 0xf0, 0xf, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xfc, 0x0, 0xf, 0x0, 0x0, 0x7c, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x3, 0xc0,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0xf, 0xb, 0xe0,
    0x3c, 0x0, 0x0, 0x78, 0x0, 0x0, 0x0, 0xf5,
    0xff, 0x43, 0xc0, 0x0, 0x7, 0x80, 0x0, 0x3f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x3, 0xf0,
    0x0, 0x2, 0xf0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x3f, 0x0, 0x3, 0xd0, 0x0,
    0x0, 0x3, 0xe0, 0x0, 0x3, 0xf0, 0x0, 0x3c,
    0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x3f, 0x80,
    0x3, 0xc0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x7,
    0xff, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0xbf, 0x3d, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0xf, 0xf1, 0xf4, 0x3d, 0x0, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0xff, 0xb, 0xc3, 0xc0,
    0x0, 0x0, 0x3, 0xc0, 0x0, 0x1d, 0xf0, 0x3c,
    0x3c, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x2, 0xcf,
    0x2, 0xc3, 0xc0, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x38, 0xf0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x7, 0xf, 0x0, 0x3, 0xd0, 0x7, 0xd0,
    0x3, 0xc0, 0x0, 0xe0, 0xf0, 0x0, 0x3c, 0x0,
    0x3c, 0x0, 0x20, 0x0, 0xc, 0xf, 0x0, 0x0,
    0x0, 0x7, 0xc0, 0x0, 0x4, 0x2, 0x80, 0xf0,
    0x0, 0x0, 0x0, 0xb8, 0x0, 0x7, 0xf4, 0x30,
    0xf, 0x0, 0x55, 0x55, 0x5f, 0x95, 0x55, 0xff,
    0xc9, 0x0, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xf, 0x0, 0x0, 0x0, 0x2e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0x7, 0xc0, 0x90, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0xf8, 0x7, 0xe4, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x3e, 0x0, 0x7, 0xf4, 0x0,
    0x0, 0xf, 0x0, 0x0, 0xf, 0x80, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0xf0, 0x0, 0x3, 0xd0, 0x0,
    0x0, 0x2f, 0xc0, 0x0, 0xf, 0x0, 0x2, 0xe0,
    0x0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0xf0, 0x2,
    0xe0, 0x0, 0x0, 0x0, 0x1, 0xf4, 0x0, 0xe,
    0x2, 0x90, 0x0, 0x0, 0x0, 0x0, 0x7, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+7259 "牙" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x1, 0x56, 0xf5, 0x55,
    0x55, 0x5f, 0x55, 0x55, 0x40, 0x0, 0x2, 0xe0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xd0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xc0, 0x0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0x0, 0xb, 0x80, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x0, 0x1e, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x0, 0xf, 0x0, 0x3, 0xfc, 0x0,
    0xbc, 0x0, 0x0, 0x0, 0xf, 0x0, 0xb, 0xfe,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x1, 0xf4, 0x0, 0x0, 0x2, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xdf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7d, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf8, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xe0, 0xf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x80, 0xf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0x0, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf4, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xd0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb8,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xd0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0,
    0x0, 0xb4, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0x7, 0x80, 0x0, 0x5, 0x40, 0x1f, 0x0,
    0x0, 0x0, 0x38, 0x0, 0x0, 0x1, 0xbf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x90, 0x0, 0x0, 0x0,

    /* U+81EA "自" */
    0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x50, 0x0,
    0x0, 0x34, 0x0, 0x0, 0x1f, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x8f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf, 0x55, 0x55, 0x55, 0x55, 0x55, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf0, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf, 0x55, 0x55, 0x55, 0x55, 0x55,
    0xf0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xf5, 0x55, 0x55,
    0x55, 0x55, 0x5f, 0xf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0xb,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0xb4, 0x0, 0x19, 0x0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0xb, 0x40, 0xb, 0xf8, 0x15, 0x55,
    0x57, 0xd5, 0x55, 0x55, 0xb9, 0x55, 0xff, 0xc3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x74,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x7, 0x40, 0x0, 0x0, 0x0, 0x0, 0x3, 0xc0,
    0x0, 0x8, 0x34, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x40, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x0, 0x3c, 0x0, 0x1f, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xe0, 0x3, 0xc0, 0x3, 0xe0, 0x0,
    0x2f, 0x40, 0x0, 0x1e, 0x0, 0x3c, 0x0, 0x3c,
    0x0, 0x7, 0xfc, 0x0, 0x1, 0xe0, 0x3, 0xc0,
    0x7, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x1e, 0x0,
    0x3c, 0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xe0, 0x3, 0xc0, 0xe, 0x4, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0x0, 0x3c, 0x2, 0xc0, 0x38, 0x0,
    0x0, 0x0, 0x1, 0xe0, 0x3, 0xc0, 0x38, 0x1,
    0xf0, 0x0, 0x0, 0x0, 0x1e, 0x0, 0x3c, 0xb,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0x1, 0xe0, 0x3,
    0xc0, 0xd0, 0x0, 0x3d, 0x0, 0x0, 0x0, 0x1e,
    0x0, 0x3c, 0x28, 0x0, 0x3, 0xe0, 0x0, 0x0,
    0x1, 0xd0, 0x3, 0xc2, 0x0, 0x0, 0x2d, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x3, 0xd5,
    0x57, 0xe5, 0x55, 0xf5, 0x55, 0xf4, 0x0, 0x0,
    0x3c, 0x0, 0x3d, 0x0, 0xf, 0x0, 0xf, 0x0,
    0x0, 0x3, 0xc0, 0x3, 0xd0, 0x0, 0xf0, 0x0,
    0xf0, 0x0, 0x0, 0x3c, 0x0, 0x3d, 0x0, 0xf,
    0x0, 0xf, 0x0, 0x0, 0x3, 0xc0, 0x3, 0xd0,
    0x0, 0xf0, 0x0, 0xf0, 0x0, 0x0, 0x3c, 0x0,
    0x3d, 0x0, 0xf, 0x0, 0xf, 0x0, 0x0, 0x3,
    0xc0, 0x3, 0xd0, 0x0, 0xf0, 0x0, 0xf0, 0x0,
    0x0, 0x3c, 0x0, 0x3d, 0x0, 0xf, 0x0, 0xf,
    0x2e, 0x0, 0x3, 0xc0, 0x3, 0xd0, 0x0, 0xf0,
    0x0, 0xf7, 0xf8, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x50,

    /* U+884C "行" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x7, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0,
    0xf8, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x3e, 0x0, 0x1, 0x55, 0x55, 0x55, 0x55,
    0x0, 0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x40, 0x2, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xf4, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xd0, 0x0, 0xf, 0xc0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x2, 0xf0,
    0x5, 0x55, 0x55, 0x55, 0xf5, 0x55, 0x40, 0x0,
    0x7d, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x0, 0xb7, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x2d, 0x3c, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x7, 0x43,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x1,
    0xc0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x0, 0x60, 0x3, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0x3, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0, 0x0,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x5, 0x40,
    0x1f, 0x0, 0x0, 0x0, 0x3, 0xc0, 0x0, 0x0,
    0x5b, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x3,
    0x80, 0x0, 0x0, 0x0, 0x2a, 0x0, 0x0, 0x0,

    /* U+98DE "飞" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x80, 0x0, 0x0,
    0x0, 0x5, 0x55, 0x55, 0x55, 0x57, 0xf8, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x3, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xd0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf4, 0x0, 0x2, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3c, 0x0, 0x1,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0x1, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xc0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf0, 0x0, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0x0, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd0, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfb, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3d,
    0x3f, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x41, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xd0, 0xf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf4, 0x0, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0x0, 0x7, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xc0, 0x0,
    0x3f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf0,
    0x0, 0x3, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7c, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf4,
    0x0, 0x0, 0x8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf4, 0x0, 0x1, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x80, 0x0, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x40, 0x38,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 207, .box_w = 5, .box_h = 6, .ofs_x = 4, .ofs_y = -1},
    {.bitmap_index = 8, .adv_w = 217, .box_w = 14, .box_h = 38, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 141, .adv_w = 343, .box_w = 18, .box_h = 31, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 281, .adv_w = 343, .box_w = 16, .box_h = 30, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 401, .adv_w = 343, .box_w = 18, .box_h = 30, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 536, .adv_w = 343, .box_w = 19, .box_h = 31, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 684, .adv_w = 343, .box_w = 20, .box_h = 29, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 829, .adv_w = 343, .box_w = 19, .box_h = 30, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 972, .adv_w = 343, .box_w = 19, .box_h = 31, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1120, .adv_w = 343, .box_w = 18, .box_h = 29, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1251, .adv_w = 343, .box_w = 18, .box_h = 31, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1391, .adv_w = 343, .box_w = 19, .box_h = 32, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1543, .adv_w = 355, .box_w = 19, .box_h = 31, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1691, .adv_w = 418, .box_w = 24, .box_h = 33, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1889, .adv_w = 620, .box_w = 37, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2084, .adv_w = 410, .box_w = 23, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2211, .adv_w = 346, .box_w = 23, .box_h = 20, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2326, .adv_w = 640, .box_w = 38, .box_h = 35, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2659, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3001, .adv_w = 640, .box_w = 38, .box_h = 38, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3362, .adv_w = 640, .box_w = 38, .box_h = 37, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3714, .adv_w = 640, .box_w = 38, .box_h = 38, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4075, .adv_w = 640, .box_w = 38, .box_h = 33, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4389, .adv_w = 640, .box_w = 37, .box_h = 36, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 4722, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 5064, .adv_w = 640, .box_w = 38, .box_h = 38, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5425, .adv_w = 640, .box_w = 37, .box_h = 38, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5777, .adv_w = 640, .box_w = 37, .box_h = 36, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 6110, .adv_w = 640, .box_w = 38, .box_h = 38, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 6471, .adv_w = 640, .box_w = 36, .box_h = 37, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 6804, .adv_w = 640, .box_w = 26, .box_h = 38, .ofs_x = 8, .ofs_y = -4},
    {.bitmap_index = 7051, .adv_w = 640, .box_w = 38, .box_h = 36, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7393, .adv_w = 640, .box_w = 38, .box_h = 37, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7745, .adv_w = 640, .box_w = 37, .box_h = 36, .ofs_x = 2, .ofs_y = -4}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x15, 0x1a, 0x22, 0x23, 0x4db7, 0x4de8, 0x4f8e,
    0x5120, 0x5255, 0x5318, 0x53a4, 0x5ead, 0x5ebc, 0x6252, 0x65cc,
    0x69ce, 0x7206, 0x8197, 0x848a, 0x87f9, 0x988b
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 46, .range_length = 12, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 83, .range_length = 39052, .glyph_id_start = 13,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 22, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    1, 16,
    1, 17,
    2, 1,
    2, 15,
    2, 16,
    13, 1,
    13, 2,
    13, 13,
    13, 14,
    13, 16,
    13, 17,
    14, 16,
    14, 17,
    15, 16,
    15, 17,
    16, 17,
    17, 1,
    17, 2,
    17, 14,
    17, 16,
    17, 17
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -30, -62, -93, -11, -10, -13, -7, -4,
    -4, -6, -11, -2, -15, -2, -15, -13,
    -65, -28, -16, -3, 8
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 21,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 2,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t ui_font_pjXiFont40 = {
#else
lv_font_t ui_font_pjXiFont40 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 41,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -4,
    .underline_thickness = 0,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if UI_FONT_PJXIFONT40*/

