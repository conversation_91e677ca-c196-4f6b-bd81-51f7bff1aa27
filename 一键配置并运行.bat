@echo off
chcp 65001 >nul
echo ========================================
echo       一键配置LVGL环境并运行模拟器
echo ========================================
echo.

:: 设置路径变量
set "SDL2_DIR=C:\SDL2"
set "SDL2_BIN=%SDL2_DIR%\x86_64-w64-mingw32\bin"
set "SDL2_LIB=%SDL2_DIR%\x86_64-w64-mingw32\lib"
set "SDL2_INC=%SDL2_DIR%\x86_64-w64-mingw32\include"
set "MINGW_BIN=C:\Users\<USER>\Desktop\mingW\mingw64\bin"

echo 🔍 检查环境...

:: 检查MinGW
if not exist "%MINGW_BIN%\gcc.exe" (
    echo ❌ 未找到MinGW编译器在: %MINGW_BIN%
    echo 请确保MinGW已正确安装
    pause
    exit /b 1
)
echo ✅ 找到MinGW编译器

:: 检查SDL2
if not exist "%SDL2_BIN%\SDL2.dll" (
    echo ❌ 未找到SDL2在: %SDL2_BIN%
    echo 请先运行 "自动安装SDL2并运行.bat"
    pause
    exit /b 1
)
echo ✅ 找到SDL2库

:: 设置环境变量（临时）
echo 🔧 配置环境变量...
set "PATH=%MINGW_BIN%;%SDL2_BIN%;%PATH%"
echo ✅ 环境变量已配置

:: 验证工具
echo 🔍 验证工具...
"%MINGW_BIN%\gcc.exe" --version | findstr gcc
if %errorlevel% neq 0 (
    echo ❌ GCC验证失败
    pause
    exit /b 1
)
echo ✅ GCC验证成功

:: 检查项目文件
echo 🔍 检查项目文件...
if not exist "simulator_main.c" (
    echo ❌ 缺少主文件: simulator_main.c
    pause
    exit /b 1
)
if not exist "lvgl" (
    echo ❌ 缺少LVGL目录
    pause
    exit /b 1
)
echo ✅ 项目文件完整

:: 复制SDL2.dll到当前目录
echo 📋 准备运行时库...
copy "%SDL2_BIN%\SDL2.dll" "." >nul 2>&1
if exist "SDL2.dll" (
    echo ✅ SDL2.dll已复制到当前目录
)

:: 配置LVGL
echo ⚙️ 配置LVGL...
if exist "lv_conf_simulator.h" (
    if exist "lvgl\lv_conf.h" (
        copy "lvgl\lv_conf.h" "lvgl\lv_conf.h.backup" >nul 2>&1
    )
    copy "lv_conf_simulator.h" "lvgl\lv_conf.h" >nul 2>&1
    echo ✅ 使用模拟器配置
)

:: 清理旧文件
echo 🧹 清理旧文件...
del /Q *.o lvgl_simulator.exe 2>nul

echo.
echo 🏗️ 开始编译...
echo.

:: 编译参数
set "CFLAGS=-std=c99 -O2 -g -Wall -Wno-unused-parameter"
set "INCLUDES=-I./lvgl -I./ -I./lvgl_user -I%SDL2_INC%"
set "LIBS=-L%SDL2_LIB% -lmingw32 -lSDL2main -lSDL2 -lm"

:: 收集源文件并编译
echo 📝 编译LVGL核心...
for %%f in (lvgl\src\core\*.c) do (
    echo 编译: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo ❌ 编译失败: %%f
        pause
        exit /b 1
    )
)

echo 📝 编译LVGL绘制...
for %%f in (lvgl\src\draw\*.c) do (
    echo 编译: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo ❌ 编译失败: %%f
        pause
        exit /b 1
    )
)

echo 📝 编译LVGL软件绘制...
for %%f in (lvgl\src\draw\sw\*.c) do (
    echo 编译: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo ❌ 编译失败: %%f
        pause
        exit /b 1
    )
)

echo 📝 编译LVGL HAL...
for %%f in (lvgl\src\hal\*.c) do (
    echo 编译: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo ❌ 编译失败: %%f
        pause
        exit /b 1
    )
)

echo 📝 编译LVGL杂项...
for %%f in (lvgl\src\misc\*.c) do (
    echo 编译: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo ❌ 编译失败: %%f
        pause
        exit /b 1
    )
)

echo 📝 编译LVGL控件...
for %%f in (lvgl\src\widgets\*.c) do (
    echo 编译: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo ❌ 编译失败: %%f
        pause
        exit /b 1
    )
)

echo 📝 编译LVGL字体...
for %%f in (lvgl\src\font\*.c) do (
    echo 编译: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo ❌ 编译失败: %%f
        pause
        exit /b 1
    )
)

echo 📝 编译用户文件...
for %%f in (lvgl_user\*.c) do (
    echo 编译: %%f
    "%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "%%f" -o "%%~nf.o"
    if %errorlevel% neq 0 (
        echo ⚠️ 跳过: %%f
    )
)

echo 📝 编译主程序...
"%MINGW_BIN%\gcc.exe" %CFLAGS% %INCLUDES% -c "simulator_main.c" -o "simulator_main.o"
if %errorlevel% neq 0 (
    echo ❌ 编译主程序失败
    pause
    exit /b 1
)

echo 🔗 链接可执行文件...
"%MINGW_BIN%\gcc.exe" *.o -o lvgl_simulator.exe %LIBS%
if %errorlevel% neq 0 (
    echo ❌ 链接失败
    pause
    exit /b 1
)

echo.
echo 🎉 编译成功！
echo.

if exist "lvgl_simulator.exe" (
    echo ✅ 可执行文件已生成: lvgl_simulator.exe
    dir lvgl_simulator.exe
    echo.
    echo 🚀 启动模拟器...
    echo 💡 操作说明:
    echo    - 鼠标左键: 模拟触摸
    echo    - 鼠标拖拽: 模拟滑动
    echo    - ESC键: 退出
    echo.
    
    start lvgl_simulator.exe
    echo ✅ 模拟器已启动！
) else (
    echo ❌ 未找到可执行文件
    pause
    exit /b 1
)

echo.
echo ✅ 配置完成！
echo 💡 下次可直接运行: lvgl_simulator.exe
pause
