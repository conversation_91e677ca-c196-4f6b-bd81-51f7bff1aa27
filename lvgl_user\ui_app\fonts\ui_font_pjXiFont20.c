/*******************************************************************************
 * Size: 20 px
 * Bpp: 2
 * Opts: --bpp 2 --size 20 --font D:\Work\LVGL\LYwatch\assets\獅尾肉丸-Light.ttf -o D:\Work\LVGL\LYwatch\assets\ui_font_pjXiFont20.c --format lvgl --symbols umSv/h --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_PJXIFONT20
#define UI_FONT_PJXIFONT20 1
#endif

#if UI_FONT_PJXIFONT20

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+002F "/" */
    0x0, 0x18, 0x0, 0x90, 0x3, 0x0, 0x8, 0x0,
    0x50, 0x3, 0x0, 0xc, 0x0, 0x60, 0x2, 0x0,
    0xc, 0x0, 0x60, 0x2, 0x40, 0xc, 0x0, 0x20,
    0x1, 0x40, 0xc, 0x0, 0x30, 0x1, 0x80, 0x8,
    0x0, 0x10, 0x0,

    /* U+0053 "S" */
    0x7, 0xfd, 0x3, 0x80, 0x34, 0x70, 0x3, 0x4a,
    0x0, 0x24, 0xb0, 0x0, 0x3, 0x80, 0x0, 0x1f,
    0x40, 0x0, 0x2f, 0x80, 0x0, 0x2f, 0x0, 0x0,
    0x38, 0x0, 0x1, 0xcc, 0x0, 0x1c, 0xd0, 0x2,
    0xce, 0x0, 0x70, 0x2f, 0xf8, 0x0,

    /* U+0068 "h" */
    0x1b, 0x0, 0x0, 0xa, 0xc0, 0x0, 0x0, 0x70,
    0x0, 0x0, 0x1c, 0x0, 0x0, 0x7, 0x0, 0x0,
    0x1, 0xc0, 0x0, 0x0, 0x70, 0xbd, 0x0, 0x1d,
    0x92, 0xd0, 0x7, 0x40, 0x38, 0x1, 0xc0, 0xa,
    0x0, 0x70, 0x2, 0x80, 0x1c, 0x0, 0xa0, 0x7,
    0x0, 0x28, 0x1, 0xc0, 0xa, 0x0, 0x70, 0x2,
    0x80, 0x1c, 0x0, 0xa0, 0x2f, 0xd1, 0xbe, 0x0,

    /* U+006D "m" */
    0x1a, 0xb, 0xd0, 0xbe, 0x0, 0xbd, 0x92, 0xcd,
    0x1e, 0x0, 0x78, 0x3, 0xc0, 0x2c, 0x1, 0xc0,
    0xd, 0x0, 0x70, 0x7, 0x0, 0x34, 0x1, 0xc0,
    0x1c, 0x0, 0xe0, 0x7, 0x0, 0x70, 0x3, 0x80,
    0x1c, 0x1, 0xc0, 0xe, 0x0, 0x70, 0x7, 0x0,
    0x38, 0x1, 0xc0, 0x1c, 0x0, 0xe0, 0x7, 0x2,
    0xfd, 0x1f, 0xe0, 0xbf, 0x40,

    /* U+0075 "u" */
    0x0, 0x0, 0x0, 0x3e, 0x1, 0xf4, 0xa, 0x0,
    0x34, 0xa, 0x0, 0x34, 0xa, 0x0, 0x34, 0xa,
    0x0, 0x34, 0xa, 0x0, 0x34, 0xa, 0x0, 0x34,
    0xa, 0x0, 0x34, 0xb, 0x0, 0x74, 0x7, 0x86,
    0x74, 0x1, 0xf8, 0x3f,

    /* U+0076 "v" */
    0x2f, 0xe0, 0xfd, 0x7, 0x0, 0x20, 0x3, 0x40,
    0x30, 0x2, 0x80, 0x50, 0x1, 0xc0, 0x80, 0x0,
    0xd0, 0x80, 0x0, 0xb1, 0x40, 0x0, 0x32, 0x0,
    0x0, 0x3a, 0x0, 0x0, 0x1d, 0x0, 0x0, 0xc,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 108, .box_w = 7, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 35, .adv_w = 178, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 73, .adv_w = 209, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 129, .adv_w = 310, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 182, .adv_w = 205, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 218, .adv_w = 173, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x24, 0x39, 0x3e, 0x46, 0x47
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 47, .range_length = 72, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 6, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 1, 2, 3, 3, 4, 5
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 1, 2, 3, 4, 5, 6
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, -5, -5, 0, -4, -2,
    -2, 0, -3, -5, 0, 0, 0, 0,
    -1, -7, 0, 0, 0, 0, 0, -7,
    -14, 0, -8, 0, -1, 4
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 5,
    .right_class_cnt     = 6,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 2,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t ui_font_pjXiFont20 = {
#else
lv_font_t ui_font_pjXiFont20 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 20,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 0,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if UI_FONT_PJXIFONT20*/

