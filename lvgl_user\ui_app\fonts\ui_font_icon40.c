/*******************************************************************************
 * Size: 40 px
 * Bpp: 2
 * Opts: --bpp 2 --size 40 --font D:\Work\LVGL\LYwatch\assets\iconfont.ttf -o D:\Work\LVGL\LYwatch\assets\ui_font_icon40.c --format lvgl -r 0xe89a -r 0xe610 -r 0xe724 -r 0xe68f -r 0xeb2c --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_ICON40
#define UI_FONT_ICON40 1
#endif

#if UI_FONT_ICON40

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+E610 "" */
    0x2a, 0xaa, 0xaa, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0x55, 0x55, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x50, 0x0, 0x8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x0, 0x0, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x50, 0x0, 0x8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x0, 0x0, 0x87, 0xb0,
    0x0, 0x29, 0xe, 0xc0, 0x50, 0x0, 0x8, 0xa1,
    0xa, 0x4e, 0x75, 0x4c, 0x5, 0x0, 0x0, 0x82,
    0xe1, 0x5c, 0xc1, 0xc1, 0xc0, 0x50, 0x0, 0x8,
    0x42, 0x89, 0xcc, 0x28, 0xa0, 0x5, 0x0, 0x0,
    0x87, 0x76, 0x9c, 0xbb, 0x5e, 0x80, 0x7a, 0xaa,
    0xb8, 0x4, 0x4, 0x41, 0x40, 0x54, 0x7, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf8, 0x0, 0x0, 0x4, 0x0,
    0x0, 0x7, 0xff, 0xff, 0x80, 0x0, 0x1, 0xc0,
    0x0, 0x0, 0x7f, 0xff, 0xf8, 0x0, 0x0, 0x3f,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x80, 0x0, 0xf,
    0xf4, 0x0, 0x0, 0x7f, 0xff, 0xf8, 0x0, 0x1,
    0xff, 0xc0, 0x0, 0x7, 0xff, 0xff, 0x80, 0x0,
    0x3f, 0xfe, 0x0, 0x0, 0x7f, 0xff, 0xf8, 0x0,
    0x7, 0xff, 0xf0, 0x0, 0x7, 0xff, 0xff, 0x80,
    0x0, 0xff, 0xff, 0x80, 0x0, 0x7f, 0xff, 0xf8,
    0x0, 0xf, 0xff, 0xfc, 0x0, 0x7, 0xff, 0xff,
    0x80, 0x1, 0xff, 0xff, 0xc0, 0x0, 0x7f, 0xff,
    0xf8, 0x0, 0x1f, 0xff, 0xfc, 0x0, 0x7, 0xff,
    0xff, 0x80, 0x1, 0xff, 0xff, 0xc0, 0x0, 0x7f,
    0xff, 0xf8, 0x0, 0xf, 0xff, 0xf8, 0x0, 0x7,
    0xff, 0xff, 0x80, 0x0, 0xbf, 0xff, 0x0, 0x0,
    0x7f, 0xff, 0xf4, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x3, 0xff, 0xff, 0x0, 0x0, 0x6, 0xa0, 0x0,
    0x0, 0x1f, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+E68F "" */
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x7, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x34, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+E724 "" */
    0x0, 0x0, 0x0, 0x6, 0xaa, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3f,
    0xfa, 0xff, 0xff, 0xff, 0xaf, 0xfe, 0x0, 0x0,
    0xff, 0xd0, 0xff, 0xff, 0xff, 0x7, 0xff, 0x40,
    0x2, 0xff, 0x40, 0x7f, 0xff, 0xfd, 0x0, 0xff,
    0xc0, 0x7, 0xfe, 0x0, 0x3f, 0xff, 0xfc, 0x0,
    0x7f, 0xe0, 0xf, 0xfc, 0x0, 0x1f, 0xff, 0xf4,
    0x0, 0x2f, 0xf0, 0xf, 0xf4, 0x0, 0xb, 0xff,
    0xe0, 0x0, 0xf, 0xf8, 0x2f, 0xf0, 0x0, 0x3,
    0xff, 0xc0, 0x0, 0x7, 0xfc, 0x3f, 0xd0, 0x0,
    0x2, 0xff, 0x80, 0x0, 0x3, 0xfc, 0x3f, 0xc0,
    0x0, 0x7, 0xff, 0xd0, 0x0, 0x2, 0xfd, 0x3f,
    0xc0, 0x0, 0xf, 0xd6, 0xf0, 0x0, 0x2, 0xfe,
    0x3f, 0xc0, 0x0, 0x2f, 0x0, 0xf8, 0x0, 0x1,
    0xfe, 0x7f, 0xc0, 0x0, 0x3d, 0x0, 0x7c, 0x0,
    0x1, 0xfe, 0x7f, 0xff, 0xff, 0xfd, 0x0, 0x7f,
    0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0xfe, 0x0,
    0xff, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff, 0xff,
    0x97, 0xff, 0xff, 0xff, 0xfe, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x2f, 0xff,
    0xff, 0xfe, 0xbe, 0xbf, 0xff, 0xff, 0xfc, 0x1f,
    0xff, 0xff, 0xfc, 0x0, 0x3f, 0xff, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0xf, 0xff, 0xff,
    0xf4, 0xb, 0xff, 0xff, 0xe0, 0x0, 0xb, 0xff,
    0xff, 0xf0, 0x3, 0xff, 0xff, 0xc0, 0x0, 0x3,
    0xff, 0xff, 0xd0, 0x1, 0xff, 0xff, 0x40, 0x0,
    0x1, 0xff, 0xff, 0xc0, 0x0, 0xbf, 0xff, 0x0,
    0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x2f, 0xfe,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0x0, 0x0, 0xb,
    0xff, 0xe4, 0x0, 0x1b, 0xff, 0xf4, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14,
    0x0, 0x0, 0x0, 0x0,

    /* U+E89A "" */
    0x0, 0x0, 0x6a, 0x40, 0x0, 0x0, 0x1a, 0x90,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xd0, 0x0, 0x7f,
    0xff, 0xe4, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xc0,
    0x2f, 0xff, 0xff, 0xe0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0x80, 0x7, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfd, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x83, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xc3, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x7f, 0xff, 0xff, 0xfd, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0x43, 0xff, 0xff, 0xff,
    0xd3, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xff,
    0xff, 0xfd, 0x3f, 0xff, 0xf8, 0xbf, 0xfe, 0x20,
    0xff, 0xff, 0xff, 0xd3, 0xff, 0xfe, 0x3, 0xff,
    0xc3, 0x87, 0xff, 0xff, 0xfc, 0x2f, 0xff, 0x81,
    0x2f, 0xfc, 0x7c, 0x3f, 0xff, 0xff, 0xc0, 0x55,
    0x50, 0xf0, 0xff, 0x4f, 0xe1, 0xfe, 0x55, 0x50,
    0x0, 0x0, 0x3f, 0x8b, 0xf0, 0xff, 0xf, 0x40,
    0x0, 0x0, 0x7f, 0xff, 0xfc, 0x3e, 0x2f, 0xf8,
    0x51, 0xff, 0xe0, 0x2, 0xff, 0xff, 0xe2, 0xd3,
    0xff, 0xc0, 0x7f, 0xfc, 0x0, 0xf, 0xff, 0xff,
    0xc, 0x7f, 0xfe, 0x1f, 0xff, 0x0, 0x0, 0x3f,
    0xff, 0xf8, 0xb, 0xff, 0xfb, 0xff, 0xd0, 0x0,
    0x1, 0xff, 0xff, 0xc0, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x7, 0xff, 0xfe, 0x1f, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf7, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x50, 0x0,
    0x0, 0x0, 0x0,

    /* U+EB2C "" */
    0x0, 0x0, 0x0, 0xaf, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xea, 0xbf, 0xf4, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x0, 0xb, 0xfd, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x1f, 0xd0, 0x0, 0x0, 0xbf, 0x40, 0x0,
    0x0, 0x2f, 0xc0, 0x0, 0x0, 0x3f, 0xf8, 0x0,
    0x0, 0x3f, 0x80, 0x0, 0x0, 0x2f, 0xff, 0x40,
    0x1, 0xff, 0x40, 0x0, 0x0, 0x1f, 0xff, 0xd0,
    0xb, 0xff, 0x40, 0x1, 0x0, 0x4, 0x6f, 0xf4,
    0x1f, 0xff, 0x40, 0xb, 0xc0, 0x0, 0x7, 0xfc,
    0x3f, 0xd0, 0x0, 0x2f, 0xf0, 0x0, 0x1, 0xfd,
    0xbf, 0x40, 0x0, 0xbf, 0xfc, 0x0, 0x0, 0xfe,
    0xfe, 0x0, 0x1, 0xff, 0xff, 0x0, 0x0, 0xbe,
    0xfd, 0x0, 0x7, 0xff, 0xff, 0xc0, 0x0, 0xbe,
    0xfd, 0x0, 0xb, 0xff, 0xff, 0xd0, 0x0, 0xfe,
    0xfe, 0x0, 0x2, 0xcf, 0xdb, 0x40, 0x0, 0xfd,
    0xbf, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x2, 0xfc,
    0x3f, 0xc0, 0x0, 0xf, 0xd0, 0x0, 0xb, 0xf8,
    0x2f, 0xfe, 0xaa, 0x4f, 0xd2, 0xaa, 0xff, 0xf0,
    0xb, 0xff, 0xff, 0x4f, 0xd3, 0xff, 0xff, 0xc0,
    0x1, 0xff, 0xff, 0x4f, 0xd3, 0xff, 0xfe, 0x0,
    0x0, 0x1a, 0xaa, 0x4a, 0x92, 0xaa, 0x90, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 640, .box_w = 34, .box_h = 36, .ofs_x = 4, .ofs_y = -3},
    {.bitmap_index = 306, .adv_w = 640, .box_w = 28, .box_h = 40, .ofs_x = 6, .ofs_y = -5},
    {.bitmap_index = 586, .adv_w = 640, .box_w = 36, .box_h = 36, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 910, .adv_w = 640, .box_w = 38, .box_h = 34, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1233, .adv_w = 640, .box_w = 32, .box_h = 24, .ofs_x = 5, .ofs_y = 3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x7f, 0x114, 0x28a, 0x51c
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 58896, .range_length = 1309, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 5, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 2,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t ui_font_icon40 = {
#else
lv_font_t ui_font_icon40 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 40,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = 0,
    .underline_thickness = 0,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if UI_FONT_ICON40*/

