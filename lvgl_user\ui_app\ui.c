// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.3.1
// LVGL version: 8.3.6
// Project name: LYwatch

#include "ui.h"
#include "ui_helpers.h"

#include "../../system_set.h"
#include "../../diver/userkey/userkey.h"
///////////////////// VARIABLES ////////////////////

// SCREEN: ui_StartScreen
void ui_StartScreen_screen_init(void);
void ui_event_StartScreen(lv_event_t * e);
lv_obj_t * ui_StartScreen;
lv_obj_t * ui_HelloLable;

// SCREEN: ui_HomeScreen
void ui_HomeScreen_screen_init(void);
void ui_event_HomeScreen(lv_event_t * e);
lv_obj_t * ui_HomeScreen;
lv_obj_t * ui_TimePanel;
lv_obj_t * ui_HourLable;
lv_obj_t * ui_MinuLable;
lv_obj_t * ui_DateLable;
lv_obj_t * ui_Dot1Panle;
lv_obj_t * ui_Dot2Panle;
lv_obj_t * ui_MainDataPanel;
lv_obj_t * ui_HeartArc;
lv_obj_t * ui_SaO2Arc;
lv_obj_t * ui_BatArc;
lv_obj_t * ui_Label7;
lv_obj_t * ui_SaO2Lable;
lv_obj_t * ui_HeartLable;
lv_obj_t * ui_Label2;
lv_obj_t * ui_Label1;
lv_obj_t * ui_BatLable;
lv_obj_t * ui_DoseRatePanel;
lv_obj_t * ui_Label3;
lv_obj_t * ui_DoseRateLable;
lv_obj_t * ui_Label4;

// SCREEN: ui_SysSetingScreen
void ui_SysSetingScreen_screen_init(void);
void ui_event_SysSetingScreen(lv_event_t * e);
lv_obj_t * ui_SysSetingScreen;
lv_obj_t * ui_SetingPanle;
lv_obj_t * ui_Label5;
lv_obj_t * ui_Label6;
lv_obj_t * ui_BTSwitch;
lv_obj_t * ui_FlyModeSwitch;
lv_obj_t * ui_GpsSwitch;
lv_obj_t * ui_SendDataNowSwitch;
lv_obj_t * ui_AutoSendSwitch;
lv_obj_t * ui_CSQLabel;
lv_obj_t * ui_GPSNumLabel;
lv_obj_t * ui_UpSendLable;
lv_obj_t * ui____initial_actions0;

///////////////////// TEST LVGL SETTINGS ////////////////////
#if LV_COLOR_DEPTH != 8
    #error "LV_COLOR_DEPTH should be 8bit to match SquareLine Studio's settings"
#endif
#if LV_COLOR_16_SWAP !=0
    #error "LV_COLOR_16_SWAP should be 0 to match SquareLine Studio's settings"
#endif

///////////////////// ANIMATIONS ////////////////////

///////////////////// FUNCTIONS ////////////////////
void ui_event_StartScreen(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_SCREEN_LOADED) { 
        if(system_state.watch_power_state == 1){
            system_state.watch_power_state = 2;
        }
        else{
            system_state.watch_power_state = 5;
        }            
    }
}
void ui_event_HomeScreen(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_LEFT) {
        lv_indev_wait_release(lv_indev_get_act());

        lv_obj_del_async(ui_HomeScreen);
        ui_SysSetingScreen_screen_init();
        _ui_screen_change(&ui_SysSetingScreen, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_SysSetingScreen_screen_init);
    }
}
void ui_event_SysSetingScreen(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    if(event_code == LV_EVENT_GESTURE &&  lv_indev_get_gesture_dir(lv_indev_get_act()) == LV_DIR_RIGHT) {
        lv_indev_wait_release(lv_indev_get_act());

        lv_obj_del_async(ui_SysSetingScreen);
        ui_HomeScreen_screen_init();
        _ui_screen_change(&ui_HomeScreen, LV_SCR_LOAD_ANIM_NONE, 0, 0, &ui_HomeScreen_screen_init);
    }
}

///////////////////// SCREENS ////////////////////

void ui_init(void)
{
    lv_disp_t * dispp = lv_disp_get_default();
    lv_theme_t * theme = lv_theme_default_init(dispp, lv_palette_main(LV_PALETTE_BLUE), lv_palette_main(LV_PALETTE_RED),
                                               true, LV_FONT_DEFAULT);
    lv_disp_set_theme(dispp, theme);
    ui_StartScreen_screen_init();
    // ui_HomeScreen_screen_init();
    // ui_SysSetingScreen_screen_init();
    ui____initial_actions0 = lv_obj_create(NULL);
    lv_disp_load_scr(ui_StartScreen);
}
